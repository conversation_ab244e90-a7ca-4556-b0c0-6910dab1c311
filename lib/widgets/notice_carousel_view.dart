import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/tools/tools.dart';

class NoticeCarouselView extends StatefulWidget {
  final List<NoticeData> noticeData;
  final Function(NoticeData)? onNoticeTap;

  const NoticeCarouselView({
    super.key,
    required this.noticeData,
    this.onNoticeTap,
  });

  @override
  NoticeCarouselViewState createState() => NoticeCarouselViewState();
}

// 暴露状态类以便父组件控制
class NoticeCarouselViewState extends State<NoticeCarouselView> with WidgetsBindingObserver {
  int _currentNoticeIndex = 0;
  Timer? _noticeTimer;
  bool _isAppInForeground = true;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 延迟启动轮播，确保页面已经渲染完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && widget.noticeData.isNotEmpty) {
        startCarousel();
      }
    });
  }

  @override
  void didUpdateWidget(NoticeCarouselView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当数据发生变化时，重新启动轮播
    if (widget.noticeData != oldWidget.noticeData) {
      _currentNoticeIndex = 0;
      startCarousel();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _noticeTimer?.cancel();
    super.dispose();
  }

  // 外部控制方法 - 启动轮播
  void startCarousel() {
    if (widget.noticeData.isEmpty || !_isAppInForeground || !_isVisible || !mounted) return;

    logger.i('NoticeCarousel 启动轮播');
    _noticeTimer?.cancel();
    _noticeTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_isAppInForeground && _isVisible && mounted) {
        _nextNotice();
      } else {
        pauseCarousel();
      }
    });
  }

  // 外部控制方法 - 暂停轮播
  void pauseCarousel() {
    logger.i('NoticeCarousel 暂停轮播');
    _noticeTimer?.cancel();
    _noticeTimer = null;
  }

  // 外部控制方法 - 设置可见性
  void setVisibility(bool isVisible) {
    if (_isVisible != isVisible) {
      logger.i('NoticeCarousel 可见性变化: $_isVisible -> $isVisible');
      _isVisible = isVisible;
      if (isVisible && _isAppInForeground) {
        startCarousel();
      } else {
        pauseCarousel();
      }
    }
  }

  // 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logger.i('NoticeCarousel 应用回到前台');
        _isAppInForeground = true;
        startCarousel();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        logger.i('NoticeCarousel 应用进入后台');
        _isAppInForeground = false;
        pauseCarousel();
        break;
    }
  }

  // 切换到下一个通知
  void _nextNotice() {
    if (widget.noticeData.isEmpty || !mounted) return;

    logger.i('_nextNotice');
    setState(() {
      _currentNoticeIndex = (_currentNoticeIndex + 1) % widget.noticeData.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.noticeData.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
      color: const Color(0xFFF5F5F5),
      child: Column(
        children: [
          // 轮播容器
          Container(
            height: 34,
            padding: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(17),
            ),
            child: Row(
              children: [
                // Text(
                //   '通知',
                //   style: TextStyle(
                //     fontSize: 14,
                //     height: 1.4,
                //     fontWeight: FontWeight.w500,
                //     color: Colors.black,
                //   ),
                // ),
                // const SizedBox(width: 10),
                Icon(Icons.campaign, size: 18, color: MColor.skin),
                const SizedBox(width: 10),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 150),
                  transitionBuilder: (Widget child, Animation<double> animation) {
                    return SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 1),
                        end: Offset.zero,
                      ).animate(animation),
                      child: child,
                    );
                  },
                  child: GestureDetector(
                    key: ValueKey(_currentNoticeIndex),
                    onTap: () {
                      if (widget.onNoticeTap != null) {
                        widget.onNoticeTap!(widget.noticeData[_currentNoticeIndex]);
                      }
                    },
                    child: Container(
                      height: 44,
                      child: Row(
                        children: [
                          Text(
                            widget.noticeData[_currentNoticeIndex].content ?? '',
                            style: TextStyle(
                              height: 1.4,
                              fontSize: 14,
                              color: MColor.xFF1B1C1A,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
