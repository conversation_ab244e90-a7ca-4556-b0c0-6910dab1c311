import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';

class VipBadge extends StatelessWidget {
  final String text;
  final double fontSize;

  const VipBadge({this.text = "VIP会员", this.fontSize = 24});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 22,
      child: Stack(
        children: [
          Row(
            children: [
              Container(
                height: 22,
                padding: EdgeInsets.only(top: 11),
                child: Container(
                  height: 11,
                  padding: EdgeInsets.symmetric(horizontal: 2),
                  decoration: BoxDecoration(
                    color: MColor.skin,
                    borderRadius: BorderRadius.circular(11),
                  ),
                  child: Text(
                    text,
                    style: TextStyle(
                      fontSize: 20,
                      height: 1.1,
                      fontWeight: FontWeight.bold,
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = 4
                        ..color = MColor.skin,
                    ),
                  ),
                ),
              ),
            ],
          ),
          Container(
            height: 22,
            child: Stack(
              children: [
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 20,
                    height: 1.1,
                    fontWeight: FontWeight.bold,
                    foreground: Paint()
                      ..style = PaintingStyle.stroke
                      ..strokeWidth = 4
                      ..color = MColor.xFFFFD180,
                  ),
                ),
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 20,
                    height: 1.1,
                    fontWeight: FontWeight.bold,
                    color: MColor.skin,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
