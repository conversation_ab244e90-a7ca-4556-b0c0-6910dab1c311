import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class AuthLoginResp extends Object {
  @Json<PERSON>ey(name: 'userInfo')
  UserInfo userInfo;

  @Json<PERSON>ey(name: 'mobileBindStatus')
  int mobileBindStatus;

  AuthLoginResp(
    this.userInfo,
    this.mobileBindStatus,
  );

  factory AuthLoginResp.fromJson(Map<String, dynamic> srcJson) => _$AuthLoginRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AuthLoginRespToJson(this);
}

@JsonSerializable()
class UserInfo extends Object {
  @Json<PERSON>ey(name: 'userId')
  int? userId;

  @JsonKey(name: 'vipType')
  String? vipType;
  @Json<PERSON>ey(name: 'mobile')
  String? mobile;

  @Json<PERSON>ey(name: 'username')
  String? userName;
  @Json<PERSON>ey(name: 'bio')
  String? bio;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'appOpenId')
  String? appOpenId;
  @Json<PERSON>ey(name: 'avatar')
  String? avatar;

  @Json<PERSON>ey(name: 'token')
  String? token;
  @JsonKey(name: 'bindWxStatus')
  int? bindWxStatus;
  @JsonKey(name: 'bindAppleStatus')
  int? bindAppleStatus;

  UserInfo(this.userId, this.vipType, this.mobile, this.bio, this.userName, this.appOpenId, this.avatar, this.token, this.bindWxStatus, this.bindAppleStatus);

  factory UserInfo.fromJson(Map<String, dynamic> srcJson) => _$UserInfoFromJson(srcJson);

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

@JsonSerializable()
class UserInfoResp extends Object {
  @JsonKey(name: 'vipMemo')
  String? vipMemo;
  @JsonKey(name: 'vipAdvertisementMemo')
  String? vipAdvertisementMemo;

  @JsonKey(name: 'accountingDays')
  int? accountingDays;

  @JsonKey(name: 'saveMoney')
  String? saveMoney;

  @JsonKey(name: 'necessaryMoney')
  String? necessaryMoney;

  @JsonKey(name: 'accountingCount')
  int? accountingCount;

  @JsonKey(name: 'userInfo')
  UserInfo? userInfo;

  @JsonKey(name: 'lastIndexBookkeepingNumber')
  String? lastIndexBookkeepingNumber;

  @JsonKey(name: 'lastStaticisticsBookkeepingNumberArr')
  String? lastStaticisticsBookkeepingNumberArr;

  @JsonKey(name: 'imageInfo')
  dynamic imageInfo;

  UserInfoResp(this.vipMemo, this.vipAdvertisementMemo, this.accountingDays, this.saveMoney, this.necessaryMoney, this.accountingCount, this.userInfo,
      this.lastIndexBookkeepingNumber, this.lastStaticisticsBookkeepingNumberArr, this.imageInfo);

  factory UserInfoResp.fromJson(Map<String, dynamic> srcJson) => _$UserInfoRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$UserInfoRespToJson(this);
}
