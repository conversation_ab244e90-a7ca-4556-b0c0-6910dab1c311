import 'package:json_annotation/json_annotation.dart';
import 'package:qiazhun/modules/plan_tab/plan_model.dart';

part 'home_detail_model.g.dart';

@JsonSerializable()
class HomeDetailResp extends Object {
  @JsonKey(name: 'assets')
  Assets? assets;

  @JsonKey(name: 'budgetArr')
  List<BudgetSetting>? budgetArr;

  @JsonKey(name: 'planArr')
  List<PlanInfo>? planArr;

  @<PERSON>son<PERSON>ey(name: 'account')
  Account? account;

  @JsonKey(name: 'flowingWaterLog')
  List<FlowingWaterLog>? flowingWaterLog;

  @Json<PERSON>ey(name: 'bookkeepingNumber')
  String? bookkeepingNumber;

  @JsonKey(name: 'indexModule')
  String? indexModule;

  HomeDetailResp(this.assets, this.budgetArr, this.planArr, this.account, this.flowingWaterLog, this.bookkeepingNumber, this.indexModule);

  factory HomeDetailResp.fromJson(Map<String, dynamic> srcJson) {
    return _$HomeDetailRespFromJson(srcJson);
  }
}

@JsonSerializable()
class Assets extends Object {
  @JsonKey(name: 'incomeTotal')
  String incomeTotal;

  @JsonKey(name: 'expenditureTotal')
  String expenditureTotal;

  @JsonKey(name: 'currentAssetsTotal')
  String currentAssetsTotal;

  Assets(
    this.incomeTotal,
    this.expenditureTotal,
    this.currentAssetsTotal,
  );

  factory Assets.fromJson(Map<String, dynamic> srcJson) => _$AssetsFromJson(srcJson);
}

@JsonSerializable()
class BudgetArr extends Object {
  @JsonKey(name: 'budgetDetail')
  List<dynamic> budgetDetail;

  BudgetArr(
    this.budgetDetail,
  );

  factory BudgetArr.fromJson(Map<String, dynamic> srcJson) => _$BudgetArrFromJson(srcJson);
}

@JsonSerializable()
class Account extends Object {
  @JsonKey(name: 'monthIncomeProportion')
  String monthIncomeProportion;
  @JsonKey(name: 'monthIncome')
  String monthIncome;

  @JsonKey(name: 'monthExpenditureProportion')
  String monthExpenditureProportion;
  @JsonKey(name: 'monthExpenditure')
  String monthExpenditure;

  @JsonKey(name: 'monthBalance')
  String monthBalance;

  Account(
    this.monthIncome,
    this.monthExpenditure,
    this.monthExpenditureProportion,
    this.monthIncomeProportion,
    this.monthBalance,
  );

  factory Account.fromJson(Map<String, dynamic> srcJson) => _$AccountFromJson(srcJson);
}

@JsonSerializable()
class FlowingWaterLog extends Object {
  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'week')
  String? week;

  @JsonKey(name: 'weekChinese')
  String? weekChinese;

  @JsonKey(name: 'item')
  List<TransactionItem>? item;
  @JsonKey(name: 'total')
  String? total;

  FlowingWaterLog(
    this.date,
    this.week,
    this.weekChinese,
    this.total,
    this.item,
  );

  factory FlowingWaterLog.fromJson(Map<String, dynamic> srcJson) => _$FlowingWaterLogFromJson(srcJson);
}

@JsonSerializable()
class TransactionItem extends Object {
  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'icon')
  String? icon;

  @JsonKey(name: 'money')
  String? money;

  @JsonKey(name: 'after')
  String? after;

  @JsonKey(name: 'memo')
  String? memo;

  @JsonKey(name: 'type')
  String? type; //1 收入 2 支出

  @JsonKey(name: 'date')
  String? date;

  @JsonKey(name: 'week')
  String? week;

  @JsonKey(name: 'categoryName')
  String? categoryName;
  @JsonKey(name: 'accountType')
  String? accountType;

  @JsonKey(name: 'accountName')
  String? accountName;
  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'isNecessaryStatus')
  String? isNecessaryStatus;

  @JsonKey(name: 'isSave')
  String? isSave;

  @JsonKey(name: 'couponPrice')
  String? couponPrice;

  TransactionItem(
    this.id,
    this.icon,
    this.money,
    this.after,
    this.memo,
    this.type,
    this.date,
    this.week,
    this.accountType,
    this.cardNo,
    this.categoryName,
    this.accountName,
    this.isNecessaryStatus,
    this.isSave,
    this.couponPrice,
  );

  factory TransactionItem.fromJson(Map<String, dynamic> srcJson) => _$TransactionItemFromJson(srcJson);
}

@JsonSerializable()
class BudgetSetting extends Object {
  @JsonKey(name: 'budgetId')
  int budgetId;

  @JsonKey(name: 'budgetType')
  String budgetType;

  @JsonKey(name: 'budgetAmount')
  String budgetAmount;

  @JsonKey(name: 'spendPrice')
  String spendPrice;

  @JsonKey(name: 'accountBookIds')
  String accountBookIds;

  @JsonKey(name: 'startDate')
  String startDate;

  @JsonKey(name: 'endDate')
  String? endDate;

  @JsonKey(name: 'expenditurePrice')
  String? expenditure;

  @JsonKey(name: 'budgetStatus')
  int? budgetStatus;

  @JsonKey(name: 'proportion')
  String? proportion;

  @JsonKey(name: 'surplusStatus')
  int? surplusStatus;
  @JsonKey(name: 'surplus')
  String? surplus;
  @JsonKey(name: 'dayPrice')
  String? dayPrice;

  BudgetSetting(
    this.budgetId,
    this.budgetType,
    this.budgetAmount,
    this.spendPrice,
    this.accountBookIds,
    this.startDate,
    this.endDate,
    this.expenditure,
    this.budgetStatus,
    this.proportion,
    this.surplus,
    this.surplusStatus,
    this.dayPrice,
  );

  factory BudgetSetting.fromJson(Map<String, dynamic> srcJson) => _$BudgetSettingFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BudgetSettingToJson(this);
}

@JsonSerializable()
class MoneyLogResp extends Object {
  @JsonKey(name: 'totalIncome')
  String? totalIncome;

  @JsonKey(name: 'totalExpense')
  String? totalExpense;
  @JsonKey(name: 'netTotal')
  String? netTotal;

  @JsonKey(name: 'list')
  List<MoneyLog>? list;

  MoneyLogResp(
    this.totalIncome,
    this.totalExpense,
    this.netTotal,
    this.list,
  );

  factory MoneyLogResp.fromJson(Map<String, dynamic> srcJson) => _$MoneyLogRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MoneyLogRespToJson(this);
}

@JsonSerializable()
class MoneyLog extends Object {
  @JsonKey(name: 'logId')
  String logId;

  @JsonKey(name: 'icon')
  String icon;

  @JsonKey(name: 'memo')
  String memo;

  @JsonKey(name: 'money')
  String money;

  @JsonKey(name: 'percentage')
  String percentage;

  @JsonKey(name: 'date')
  String date;

  @JsonKey(name: 'actualPercentage')
  String actualPercentage;

  @JsonKey(name: 'type')
  String type;

  MoneyLog(
    this.logId,
    this.icon,
    this.memo,
    this.money,
    this.percentage,
    this.date,
    this.actualPercentage,
    this.type,
  );

  factory MoneyLog.fromJson(Map<String, dynamic> srcJson) => _$MoneyLogFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MoneyLogToJson(this);
}

@JsonSerializable()
class NoticeData {
  @JsonKey(name: 'type')
  dynamic type; //1表示还款提醒// 2表示讨账提醒  // 3表示年费提醒

  @JsonKey(name: 'title')
  String? title;

  @JsonKey(name: 'content')
  String? content;

  @JsonKey(name: 'jumpPath')
  String? jumpPath;

  NoticeData(
    this.title,
    this.content,
    this.jumpPath,
    this.type,
  );

  factory NoticeData.fromJson(Map<String, dynamic> srcJson) => _$NoticeDataFromJson(srcJson);

  Map<String, dynamic> toJson() => _$NoticeDataToJson(this);
}

@JsonSerializable()
class CategoryFlowLog extends Object {
  @JsonKey(name: 'id')
  dynamic id;

  @JsonKey(name: 'icon')
  String icon;

  @JsonKey(name: 'money')
  String money;

  @JsonKey(name: 'coupon_price')
  String couponPrice;

  @JsonKey(name: 'memo')
  String memo;

  @JsonKey(name: 'type')
  String type;

  @JsonKey(name: 'account_id')
  String accountId;

  @JsonKey(name: 'createtime')
  String createtime;

  @JsonKey(name: 'percent')
  String percent;
  @JsonKey(name: 'actualPercentage')
  String actualPercentage;

  CategoryFlowLog(
    this.id,
    this.icon,
    this.money,
    this.couponPrice,
    this.memo,
    this.type,
    this.accountId,
    this.createtime,
    this.percent,
    this.actualPercentage,
  );

  factory CategoryFlowLog.fromJson(Map<String, dynamic> srcJson) => _$CategoryFlowLogFromJson(srcJson);

  Map<String, dynamic> toJson() => _$CategoryFlowLogToJson(this);
}
