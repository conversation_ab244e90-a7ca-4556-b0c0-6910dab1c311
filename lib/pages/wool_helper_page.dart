import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class WoolHelperPage extends StatefulWidget {
  const WoolHelperPage({super.key});

  @override
  State<StatefulWidget> createState() => _WoolHelperState();
}

class _WoolHelperState extends State<WoolHelperPage> {
  bool _isLoading = true;
  List<WoolHelperData> _unusedData = [];
  List<WoolHelperData> _usedData = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Call the wool helper API
      final response = await UserRepo.getWoolHelper();

      if (response.code == 1) {
        setState(() {
          _unusedData = response.data?.unused ?? [];
          _usedData = response.data?.used ?? [];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
        showToast(response.msg ?? '获取数据失败');
      }
      // if (response['code'] == 1) {
      //   setState(() {
      //     _woolHelperData = response['data'] ?? [];
      //     _isLoading = false;
      //   });
      // } else {
      //   setState(() {
      //     _isLoading = false;
      //   });
      //   showToast(response['msg'] ?? '获取数据失败');
      // }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast('网络错误，请稍后重试');
      logger.e('Wool helper error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
        title: '羊毛助手',
        body: Builder(builder: (context) {
          if (_isLoading) {
            return LoadingView();
          }
          if (_usedData.isEmpty && _unusedData.isEmpty) {
            return EmptyView();
          }
          return ListView.separated(
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return Container(
                    color: MColor.xFFF5F5F5,
                    child: _itemView(
                        index >= _unusedData.length ? _usedData[index - _unusedData.length] : _unusedData[index], index, index >= _unusedData.length));
              },
              separatorBuilder: (context, index) {
                if (_usedData.isEmpty || _unusedData.isEmpty) {
                  return const SizedBox(
                    height: 18,
                  );
                } else if (index == _unusedData.length - 1) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 18.0),
                    child: Image.asset(
                      'assets/images/bg_separator_repay_diary.png',
                      width: 343,
                      height: 31,
                    ),
                  );
                }
                return const SizedBox(
                  height: 18,
                );
              },
              itemCount: _unusedData.length + _usedData.length);
        }));
  }

  Widget _itemView(WoolHelperData data, int index, bool isUsed) {
    return Slidable(
      key: ValueKey(data.cardId),
      enabled: true,
      // The end action pane is the one at the right or the bottom side.
      endActionPane: ActionPane(
        motion: ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (context) async {
              RouterHelper.router.pushNamed(Routes.editAccountPath, extra: {'accountId': data.cardId}).then((value) {
                _loadData();
              });
            },
            backgroundColor: MColor.xFFFFBE4A,
            foregroundColor: MColor.xFFFFFFFF,
            icon: Icons.edit,
            label: '编辑',
          ),
        ],
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              RouterHelper.router.pushNamed(Routes.accountDetailPath, extra: {'accountId': data.cardId}).then((_) {
                _loadData();
              });
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 14),
              padding: EdgeInsets.all(18),
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Color(int.tryParse(data.bankColour ?? '', radix: 16) ?? 0xFF5CB1AC)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  RoundImage(imageUrl: getImageUrl(data.bankIcon ?? ''), radius: 20, size: 40),
                  const SizedBox(
                    width: 10,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        data.accountName ?? '',
                        style: TextStyle(height: 1.4, fontSize: 15, fontWeight: FontWeight.bold, color: MColor.xFFFFFFFF),
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Text(
                        data.cardType ?? '',
                        style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.bold, color: MColor.xFF99CEC2),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (data.wools?.isNotEmpty == true) ...{
            const SizedBox(
              height: 14,
            ),
            SizedBox(
              height: 30,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 14),
                itemCount: data.wools!.length,
                itemBuilder: (context, index) => _woolItemView(data.wools![index]),
                separatorBuilder: (context, index) => const SizedBox(width: 12),
              ),
            ),
          }
        ],
      ),
    );
  }

  Widget _woolItemView(Wools wool) {
    return GestureDetector(
      onTap: () {
        if (wool.status != '1') {
          _showWoolMenu(wool);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: wool.status != '2' ? MColor.xFFE8E8E8 : MColor.xFFF1E4D4,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              wool.content?.name ?? '',
              style: TextStyle(
                fontSize: 11,
                color: wool.status != '2' ? MColor.xFF999999 : MColor.xFFE0A455,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(
              width: 2,
            ),
            Text(
              '${wool.progress?.typeText} ${wool.progress?.current ?? ''}/${wool.progress?.target ?? ''}${wool.progress?.unit ?? ''}',
              style: TextStyle(
                fontSize: 11,
                color: wool.status != '2' ? MColor.xFF999999 : MColor.xFFE0A455,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _showWoolMenu(Wools wool) {
    // 判断当前状态：2=未使用，3=已使用
    bool isUsed = wool.status == '3';
    String targetStatus = isUsed ? '未使用' : '已使用';
    String targetType = isUsed ? '2' : '3';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('状态切换'),
          content: Text('是否切换为$targetStatus？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _markWoolAsUsed(wool, targetType);
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _markWoolAsUsed(Wools wool, String type) async {
    try {
      final response = await UserRepo.markWoolAsUsed(wool.id, type);

      if (response.code == 1) {
        showToast('标记成功');
        _loadData(); // 刷新整个页面
      } else {
        showToast(response.msg ?? '标记失败');
      }
    } catch (e) {
      showToast('网络错误，请稍后重试');
      logger.e('Mark wool as used error: $e');
    }
  }
}
