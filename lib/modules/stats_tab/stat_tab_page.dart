import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/modules/stats_tab/stat_chart_view.dart';
import 'package:qiazhun/modules/stats_tab/stat_model.dart';
import 'package:qiazhun/modules/stats_tab/stat_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/date_range_view.dart';
import 'package:qiazhun/widgets/legend_widget.dart';
import 'package:qiazhun/widgets/round_image.dart';

class StatisticsTabPage extends StatefulWidget {
  const StatisticsTabPage({super.key});

  @override
  State<StatefulWidget> createState() => _StatisticsTabState();
}

class _StatisticsTabState extends State<StatisticsTabPage> {
  //初始化为月
  int _dateRangeType = 2;

  DateTime _dateTimeStart = DateTime(DateTime.now().year, DateTime.now().month, 1);
  DateTime _dateTimeEnd = DateTime.now();

  List<ChartIndexResult> _chartIndexResults = [];
  String? _statTotal;
  IncomeOutcomeItems? _incomeItems;
  IncomeOutcomeItems? _expenditureItems;
  PropertyTrendResp? _propertyTrendResp;
  // List<PropertyTrendItem> _propertyTrendItems = [];
  PropertySummaryResp? _propertySummaryResp;
  PropertySummaryResp? _debtSummaryResp;
  // LiabilityResp? _liabilityResp;
  SaveUnnecessaryResp? _saveUnnecessaryResp;

  String? _tmpBookkeepingNumbers;

  final GlobalKey<RefreshIndicatorState> _refreshKey = GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _tmpBookkeepingNumbers = UserStore.to.lastStaticisticsBookkeepingNumberArr;
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _refreshKey.currentState?.show();
    });
  }

  String get _timeRangeInterval {
    DateFormat dt = DateFormat('yyyy-MM-dd');
    String timeInterval = '';
    DateTime begin = DateTime(_dateTimeStart.year, _dateTimeStart.month, _dateTimeStart.day);
    DateTime end = DateTime(_dateTimeEnd.year, _dateTimeEnd.month, _dateTimeEnd.day);
    timeInterval = '${dt.format(begin)},${dt.format(end)}';
    return timeInterval;
  }

  Future<void> _getData() async {
    try {
      String timeInterval = _timeRangeInterval;
      var resps = await Future.wait([
        StatRepo.getChartIndex(bookkeepingNumber: _tmpBookkeepingNumbers, type: '${_dateRangeType + 1}', timeInterval: timeInterval),
        StatRepo.getIncomeAndPayment(bookkeepingNumber: _tmpBookkeepingNumbers, type: '${_dateRangeType + 1}', timeInterval: timeInterval),
        StatRepo.getPropertyTrend(type: '${_dateRangeType + 1}', timeInterval: timeInterval),
        StatRepo.getPropertySummary(),
        StatRepo.getLiabilitiesSummary(),
        StatRepo.getSavingAndUnnecessary(bookkeepingNumber: _tmpBookkeepingNumbers, type: '${_dateRangeType + 1}', timeInterval: timeInterval)
      ]);
      if (resps.isNotEmpty) {
        if (resps[0].code == 1) {
          var resp = (resps[0].data as ChartIndexResp?);
          if (resp?.bookkeepingNumber?.isNotEmpty == true) {
            UserStore.to.lastStaticisticsBookkeepingNumberArr = resp!.bookkeepingNumber;
          }
          _statTotal = resp?.total;
          _chartIndexResults.clear();
          if (resp?.result?.isNotEmpty == true) {
            _chartIndexResults.addAll(resp!.result!);
          }
        }
        if (resps[1].code == 1) {
          var resp = (resps[1].data as IncomePaymentResp?);
          _incomeItems = resp?.incomeItems;
          _expenditureItems = resp?.expenditureItems;
        }
        if (resps[2].code == 1) {
          _propertyTrendResp = resps[2].data as PropertyTrendResp;
        }
        if (resps[3].code == 1) {
          _propertySummaryResp = resps[3].data as PropertySummaryResp;
        }
        if (resps[4].code == 1) {
          _debtSummaryResp = resps[4].data as PropertySummaryResp;
        }
        if (resps[5].code == 1) {
          _saveUnnecessaryResp = (resps[5].data as SaveUnnecessaryResp);
        }
      }
      setState(() {});
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('StatTabPage _getData error $e');
      showToast('获取数据失败 $e');
    } finally {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      actions: [
                        IconButton(
                            // padding: EdgeInsets.zero,
                            // constraints: const BoxConstraints(),
                            // style: const ButtonStyle(
                            //   tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                            // ),
                            onPressed: () {
                              List<String> selected = [];
                              UserStore.to.lastStaticisticsBookkeepingNumberArr?.split(',').forEach((el) {
                                selected.add(el);
                              });
                              RouterHelper.router
                                  .pushNamed(Routes.chooseLedgarPath, extra: {'selected': selected, 'multiSelect': true, 'createNew': true}).then((value) {
                                if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
                                  _tmpBookkeepingNumbers = value['selected'].join(',');
                                  _refreshKey.currentState?.show();
                                }
                              });
                            },
                            icon: Image.asset(
                              'assets/images/ic_ledger_2.png',
                              width: 24,
                              height: 24,
                              fit: BoxFit.fill,
                            )),
                      ],
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '账本列表',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: RefreshIndicator(
                    key: _refreshKey,
                    triggerMode: RefreshIndicatorTriggerMode.anywhere,
                    onRefresh: () async {
                      await _getData();
                    },
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          // _timeSelectionView,
                          DateRangeView(
                              dateRangeType: _dateRangeType,
                              dateTimeStart: _dateTimeStart,
                              dateTimeEnd: _dateTimeEnd,
                              onDateRangeChanged: (type, start, end) {
                                setState(() {
                                  _dateRangeType = type;
                                  _dateTimeStart = start;
                                  _dateTimeEnd = end;
                                  _getData();
                                });
                              }),
                          const SizedBox(
                            height: 14,
                          ),
                          _barChartView,
                          const SizedBox(
                            height: 14,
                          ),
                          _savingAndUnnecessaryView,
                          const SizedBox(
                            height: 14,
                          ),
                          _incomeView,
                          const SizedBox(
                            height: 14,
                          ),
                          _outcomeView,
                          const SizedBox(
                            height: 14,
                          ),
                          _summaryView,
                          const SizedBox(
                            height: 14,
                          ),
                          _debtView,
                          const SizedBox(
                            height: 14,
                          ),
                          _trendingView,
                        ],
                      ),
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _barChartView {
    const headerTitle = ['本日统计', '本周统计', '本月统计', '本年统计', '收支统计'];
    return Container(
        decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
        padding: EdgeInsets.all(14),
        margin: EdgeInsets.symmetric(horizontal: 14),
        child: Column(children: [
          _SectionHeaderView(headerTitle[_dateRangeType], _statTotal == null ? '' : '¥$_statTotal'),
          const SizedBox(
            height: 16,
          ),
          if (_chartIndexResults.isNotEmpty) StatChartView(_chartIndexResults, _dateTimeStart, _dateTimeEnd, _dateRangeType)
        ]));
  }

  Widget get _savingAndUnnecessaryView {
    if (_saveUnnecessaryResp == null) {
      return const SizedBox();
    }
    return Container(
      // color: MColor.xFFF5F5F5,
      child: Row(
        children: [
          const SizedBox(
            width: 14,
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                RouterHelper.router.pushNamed(Routes.savingPath, extra: {'page_type': 0}).then((value) {
                  _getData();
                });
              },
              child: Container(
                height: 70,
                // padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20), border: Border.all(color: MColor.skin, width: 1), color: MColor.skin.withOpacity(0.2)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _saveUnnecessaryResp!.saveDepositTotal ?? '',
                      style: TextStyle(fontSize: 22, height: 1.4, color: MColor.skin),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Text(
                      '节省金额',
                      style: TextStyle(fontSize: 12, height: 1.4, color: MColor.skin),
                    )
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 14,
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                RouterHelper.router.pushNamed(Routes.savingPath, extra: {'page_type': 1}).then((value) {
                  _getData();
                });
              },
              child: Container(
                height: 70,
                // padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20), border: Border.all(color: MColor.xFFFED58E, width: 1), color: MColor.xFFFED58E.withOpacity(0.2)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _saveUnnecessaryResp!.necessaryTotal ?? '',
                      style: TextStyle(fontSize: 22, height: 1.4, color: MColor.xFFFFD180),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Text(
                      '非必要金额',
                      style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFFFFD180),
                    )
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 14,
          )
        ],
      ),
    );
  }

  Widget get _trendingView {
    if (_propertyTrendResp?.result?.isNotEmpty != true) {
      return const SizedBox();
    }
    if (_propertyTrendResp!.result!.length < 2) {
      return const SizedBox();
    }
    List<List<double>> dataGroups = [];
    double _maxData, _minData;
    _maxData = double.tryParse(_propertyTrendResp?.result?[0].netAssets ?? '') ?? 0.0;
    _minData = _maxData;
    // _maxData = 0;
    // _minData = 100;
    _propertyTrendResp?.result?.asMap().forEach((index, el) {
      double y1 = double.tryParse(el.netAssets ?? '') ?? 0.0;
      // TODO 测试数据
      // y1 = index + Random().nextInt(20) * 1.0;
      // y2 = index + Random().nextInt(20) * 1.0;
      // TODO end
      _maxData = max(y1, _maxData);
      _minData = min(y1, _minData);
      dataGroups.add([y1, 0]);
    });
    if (_minData > 0) {
      _minData = 0;
    }

    List<String> bottomTilesStr = [];

    if (_dateRangeType == 2 || _dateRangeType == 4) {
      var dataCount = _propertyTrendResp?.result?.length ?? 0;
      var maxBottomTiles = 5;

      DateFormat df = DateFormat('dd');
      List.generate(dataCount, (i) {
        if (dataCount >= maxBottomTiles) {
          var step = (dataCount / (maxBottomTiles - 1)).toInt();
          if (i % step == 0) {
            bottomTilesStr.add(df.format(_dateTimeStart.add(Duration(days: i))));
          } else {
            bottomTilesStr.add('');
          }
        } else {
          bottomTilesStr.add(df.format(_dateTimeStart.add(Duration(days: i))));
        }
      });
    } else {
      if (_dateRangeType == 1) {
        bottomTilesStr.addAll(['周一', '周二', '周三', '周四', '周五', '周六', '周日']);
      } else if (_dateRangeType == 3) {
        List.generate(12, (i) {
          if (i % 2 == 0) {
            bottomTilesStr.add('${i + 1}月');
          } else {
            bottomTilesStr.add('');
          }
        });
      }
    }
    return Container(
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
      padding: EdgeInsets.all(14),
      margin: EdgeInsets.symmetric(horizontal: 14),
      child: Column(
        children: [
          _SectionHeaderView('资产走势', '¥${_propertyTrendResp?.total ?? ''}'),
          const SizedBox(
            height: 12,
          ),
          StatLineChartView(
            _maxData,
            _minData,
            bottomTilesStr,
            dataGroups,
            showTitles: false,
            showSingleLine: true,
          )
        ],
      ),
    );
  }

  Widget get _incomeView {
    return _IncomeOutcomeView('收入来源', _incomeItems, _dateRangeType, _dateTimeStart, _dateTimeEnd, MColor.skin);
    // if (_incomeItems?.items?.isNotEmpty != true) {
    //   return const SizedBox();
    // }
    // List.generate(_incomeItems!.items?.length ?? 0, (index) {});
    // return Container(
    //   decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
    //   padding: EdgeInsets.all(14),
    //   margin: EdgeInsets.symmetric(horizontal: 14),
    //   child: Column(
    //     children: [
    //       _SectionHeaderView('收入来源', '¥${_incomeItems!.total ?? '0.00'}'),
    //       for (var item in _incomeItems?.items ?? []) ...{
    //         const SizedBox(
    //           height: 15,
    //         ),
    //         GestureDetector(
    //           onTap: () {
    //             RouterHelper.router.pushNamed(Routes.categoryWaterFlowPath, extra: {
    //               'title': item['categoryName'],
    //               'categoryId': item['categoryId']?.toString(),
    //               'page_type': 2,
    //               'date_range_type': _dateRangeType,
    //               'date_range_begin': _dateTimeStart,
    //               'date_range_end': _dateTimeEnd
    //             });
    //           },
    //           child: _BarItemView(
    //               imageUrl: item['categoryIcon'], headerText: item['categoryName'], amount: item['money'], percent: item['proportion'], barColor: MColor.skin),
    //         )
    //       },
    //     ],
    //   ),
    // );
  }

  Widget get _outcomeView {
    return _IncomeOutcomeView('支出分布', _expenditureItems, _dateRangeType, _dateTimeStart, _dateTimeEnd, MColor.xFFFED58E);
    // return Container(
    //   decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
    //   padding: EdgeInsets.all(14),
    //   margin: EdgeInsets.symmetric(horizontal: 14),
    //   child: Column(
    //     children: [
    //       _SectionHeaderView('支出分布', '¥${_expenditureItems?.total ?? '0.00'}'),
    //       for (var item in _expenditureItems?.items ?? []) ...{
    //         const SizedBox(
    //           height: 15,
    //         ),
    //         GestureDetector(
    //           behavior: HitTestBehavior.translucent,
    //           onTap: () {
    //             RouterHelper.router.pushNamed(Routes.categoryWaterFlowPath, extra: {
    //               'title': item['categoryName'],
    //               'categoryId': item['categoryId']?.toString(),
    //               'page_type': 1,
    //               'date_range_type': _dateRangeType,
    //               'date_range_begin': _dateTimeStart,
    //               'date_range_end': _dateTimeEnd
    //             });
    //           },
    //           child: _BarItemView(
    //               imageUrl: item['categoryIcon'],
    //               headerText: item['categoryName'],
    //               amount: item['money'],
    //               percent: item['proportion'],
    //               barColor: MColor.xFFFED58E),
    //         )
    //       },
    //     ],
    //   ),
    // );
  }

  final _summaryColors = {
    '1': MColor.xFFF5D16D,
    '2': MColor.xFF577F8C,
    '4': MColor.xFFD05363,
    '3': MColor.xFF1C6974,
    '6': MColor.xFFFB9186,
    '5': MColor.xFF5CB188,
    '8': MColor.skin
  };

  Widget get _summaryView {
    if (_propertySummaryResp?.result?.isNotEmpty != true) {
      return const SizedBox();
    }
    List<double> percentages = [];
    List<String> titles = [];
    List<Color> colors = [];
    _propertySummaryResp!.result?.every((item) {
      double percent = (double.tryParse(item.proportion ?? '') ?? 0);
      percentages.add(percent);
      titles.add(item.proportion ?? '0');
      colors.add(_summaryColors[item.accountType ?? '1'] ?? MColor.xFFF5D16D);
      return true;
    });

    return Container(
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
      padding: EdgeInsets.all(14),
      margin: EdgeInsets.symmetric(horizontal: 14),
      child: Column(
        children: [
          _SectionHeaderView('资产汇总', ''),
          const SizedBox(
            height: 12,
          ),
          StatPieChartView(
            titles: titles,
            percentages: percentages,
            colors: colors,
            radius: 50,
            innerRaidus: 50,
          ),
          for (PropertySummaryItem item in _propertySummaryResp!.result ?? []) ...{
            const SizedBox(
              height: 15,
            ),
            _BarItemView(
                imageUrl: getImageUrl(item.icon),
                headerText: item.accountName,
                amount: item.money,
                percent: item.proportion,
                barColor: _summaryColors[item.accountType ?? '1'],
                amountColor: MColor.xFF1B1C1A,
                amountWeight: FontWeight.bold),
          },
        ],
      ),
    );
  }

  Widget get _debtView {
    if (_debtSummaryResp?.result?.isNotEmpty != true) {
      return const SizedBox();
    }
    List<double> percentages = [];
    List<String> titles = [];
    List<Color> colors = [];
    _debtSummaryResp!.result?.every((item) {
      double percent = (double.tryParse(item.proportion ?? '') ?? 0);
      percentages.add(percent);
      titles.add(item.proportion ?? '0');
      colors.add(_summaryColors[item.accountType ?? '1'] ?? MColor.xFFF5D16D);
      return true;
    });
    return Container(
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
      padding: EdgeInsets.all(14),
      margin: EdgeInsets.symmetric(horizontal: 14),
      child: Column(
        children: [
          _SectionHeaderView('负债汇总', '¥${_debtSummaryResp?.total ?? ''}'),
          if (percentages.isNotEmpty) ...{
            const SizedBox(
              height: 12,
            ),
            StatPieChartView(
              titles: titles,
              percentages: percentages,
              colors: colors,
              radius: 50,
              innerRaidus: 50,
            ),
          },
          for (PropertySummaryItem item in _debtSummaryResp!.result ?? []) ...{
            const SizedBox(
              height: 15,
            ),
            _BarItemView(
                imageUrl: getImageUrl(item.icon),
                headerText: item.accountName,
                amount: item.money,
                percent: item.proportion,
                barColor: _summaryColors[item.accountType ?? '1'],
                amountColor: MColor.xFF1B1C1A,
                amountWeight: FontWeight.bold),
          },
        ],
      ),
    );
  }
}

class _IncomeOutcomeView extends StatefulWidget {
  final String title;
  final IncomeOutcomeItems? _incomeItems;
  final int _dateRangeType;
  final DateTime _dateTimeStart;
  final DateTime _dateTimeEnd;
  final Color barColor;

  _IncomeOutcomeView(this.title, this._incomeItems, this._dateRangeType, this._dateTimeStart, this._dateTimeEnd, this.barColor);

  @override
  State<StatefulWidget> createState() {
    return _IncomeOutcomeState();
  }
}

class _IncomeOutcomeState extends State<_IncomeOutcomeView> {
  bool _isExpanded = false;

  late bool _needShowMore = false;

  @override
  initState() {
    super.initState();
    _needShowMore = (widget._incomeItems?.items?.length ?? 0) > 5;
  }

  @override
  void didUpdateWidget(covariant _IncomeOutcomeView oldWidget) {
    super.didUpdateWidget(oldWidget);
    _needShowMore = (widget._incomeItems?.items?.length ?? 0) > 5;
    _isExpanded = false;
  }

  @override
  Widget build(BuildContext context) {
    if (widget._incomeItems?.items?.isNotEmpty != true) {
      return const SizedBox();
    }
    int length = (widget._incomeItems?.items?.length ?? 0);
    if (_needShowMore && !_isExpanded) {
      length = min(5, length);
    }
    return Container(
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(28)),
      padding: EdgeInsets.all(14),
      margin: EdgeInsets.symmetric(horizontal: 14),
      child: Column(
        children: [
          _SectionHeaderView(widget.title, '¥${widget._incomeItems!.total ?? '0.00'}'),
          for (int i = 0; i < length; i++) ...{
            const SizedBox(
              height: 15,
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                RouterHelper.router.pushNamed(Routes.categoryWaterFlowPath, extra: {
                  'title': widget._incomeItems!.items![i]['categoryName'],
                  'categoryId': widget._incomeItems!.items![i]['categoryId']?.toString(),
                  'page_type': 2,
                  'date_range_type': widget._dateRangeType,
                  'date_range_begin': widget._dateTimeStart,
                  'date_range_end': widget._dateTimeEnd
                });
              },
              child: _BarItemView(
                  imageUrl: widget._incomeItems?.items![i]['categoryIcon'],
                  headerText: widget._incomeItems?.items![i]['categoryName'],
                  amount: widget._incomeItems?.items![i]['money'],
                  percent: widget._incomeItems?.items![i]['proportion'],
                  barColor: widget.barColor),
            )
          },
          if (_needShowMore) ...{
            const SizedBox(
              height: 15,
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: Text(
                _isExpanded ? '收起' : '查看更多',
                style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 14, height: 1.4),
              ),
            )
          },
        ],
      ),
    );
  }
}

class _SectionHeaderView extends StatelessWidget {
  final String leadingText;
  final String endingText;

  _SectionHeaderView(this.leadingText, this.endingText);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          height: 13,
          width: 3,
          decoration: BoxDecoration(color: MColor.xFFFFD180, borderRadius: BorderRadius.circular(2)),
        ),
        const SizedBox(
          width: 7,
        ),
        Text(
          leadingText,
          style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 15, height: 1.4),
        ),
        const Spacer(),
        Text(
          endingText,
          style: TextStyle(fontSize: 16, height: 1.4, color: MColor.xFF1B1C1A),
        )
      ],
    );
  }
}

class _BarItemView extends StatelessWidget {
  final String? imageUrl;
  final String? headerText;
  final String? amount;
  final String? percent;
  final Color? barColor;
  final Color? amountColor;
  final FontWeight? amountWeight;

  _BarItemView({this.imageUrl, this.headerText, this.amount, this.percent, this.barColor, this.amountColor = MColor.xFF1B1C1A, this.amountWeight});

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      double barWidth = MediaQuery.of(context).size.width - 28 - 28 - 32 - 12 - 60;
      return Container(
        // color: Colors.yellow,
        child: Row(
          children: [
            RoundImage(imageUrl: getImageUrl(imageUrl), radius: 16, size: 32),
            const SizedBox(
              width: 12,
            ),
            Expanded(
                child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      headerText ?? '',
                      style: TextStyle(fontSize: 14, height: 1.4, color: MColor.xFF1B1C1A),
                    ),
                    Text(amount ?? '', style: TextStyle(fontSize: 15, height: 1.4, color: amountColor, fontWeight: amountWeight))
                  ],
                ),
                const SizedBox(
                  height: 2,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                        height: 10,
                        child: Stack(
                          children: [
                            Container(
                              width: barWidth,
                              decoration: BoxDecoration(color: MColor.xFFF5F5F5, borderRadius: BorderRadius.circular(10)),
                            ),
                            Container(
                              width: barWidth * (double.tryParse(percent ?? '') ?? 0) / 100,
                              decoration: BoxDecoration(color: barColor, borderRadius: BorderRadius.circular(10)),
                            )
                          ],
                        )),
                    Text(
                      '$percent%',
                      style: TextStyle(color: MColor.xFF999999, fontSize: 12, height: 1.4),
                    )
                  ],
                )
              ],
            ))
          ],
        ),
      );
    });
  }
}
