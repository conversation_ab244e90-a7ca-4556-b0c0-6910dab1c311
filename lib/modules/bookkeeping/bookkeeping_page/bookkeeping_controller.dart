import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class CategoryAndAccount {
  final CategoryItem? category;
  final AccountModel? account;
  CategoryAndAccount(this.category, this.account);
}

class BookkeepingController extends GetxController {
  final dynamic logId;
  final String? targetAccountId;

  BookkeepingController({this.logId, this.targetAccountId});

  final Rx<DateTime> selectedDay = DateTime.now().obs;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  final TextEditingController textController = TextEditingController();
  final FocusNode textNode = FocusNode();

  final ScrollController shortcutScrollController = ScrollController();

  //账户
  final firstIncomeAccount = Rxn<AccountModel>();
  final firstOutcomeAccount = Rxn<AccountModel>();

  //当前选中的账户
  final selectedIncomeAccount = Rxn<AccountModel>();
  final selectedOutcomeAccount = Rxn<AccountModel>();

  final accountWaterflow = Rxn<AccountModel>();

  //账本
  final bookkeepingInfo = Rxn<BookkeepingInfo>();
  //分类
  final selectedOutcomeCategory = Rxn<CategoryItem>();
  final selectedIncomeCategory = Rxn<CategoryItem>();

  final outcomeCategories = RxList<CategoryItem>();
  final incomeCategories = RxList<CategoryItem>();

  // 快捷账户相关
  BookkeepingContextResp? _bookkeepingContext;
  final shortcutIncomeAccounts = RxList<AccountModel>([]);
  final shortcutOutcomeAccounts = RxList<AccountModel>([]);

  final RxInt selectedTab = 0.obs;
  final bookkeepingResult = Rxn<double>();
  final savingResult = Rxn<double>();
  final noNeed = false.obs;
  final RxBool isSavingInput = false.obs;
  final remarkStr = ''.obs;

  final PageController pageController = PageController();
  final currentOutcomePage = 0.obs;
  final currentIncomePage = 0.obs;

  final isShortcutManageMode = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadData(requestDetail: logId != null);
  }

  @override
  void onClose() {
    logger.i('onClose');
    textNode.dispose();
    textController.dispose();
    pageController.dispose();
    shortcutScrollController.dispose();
    super.onClose();
  }

  Future<void> _loadData({bool? requestDetail}) async {
    // Loading.show();
    List<Future<dynamic>> requests = [
      BookkeepingStore.to.getMyBookkeepingCategory(),
      BookkeepingStore.to.getAccountList(),
      BookkeepingStore.to.getBookkeepingList(),
      BookkeepingRepo.getBookkeepingContext(accountId: targetAccountId, moneyLogId: logId),
    ];
    // if (requestDetail == true) {
    //   requests.add(BookkeepingRepo.billDetail(logId));
    // }
    var resp = await Future.wait(requests);

    final bookkeepingList = BookkeepingStore.to.bookkeepingList;
    // 处理记账上下文响应
    BaseModel<BookkeepingContextResp> contextResp = resp.last as BaseModel<BookkeepingContextResp>;
    if (contextResp.code == 1) {
      _bookkeepingContext = contextResp.data;
      shortcutIncomeAccounts.clear();
      shortcutOutcomeAccounts.clear();
      shortcutIncomeAccounts.addAll(_bookkeepingContext?.shortcutAccounts?.income ?? []);
      shortcutOutcomeAccounts.addAll(_bookkeepingContext?.shortcutAccounts?.outcome ?? []);

      incomeCategories.value = _bookkeepingContext?.categoryList?.income ?? [];
      outcomeCategories.value = _bookkeepingContext?.categoryList?.outcome ?? [];

      selectedOutcomeCategory.value = outcomeCategories.isNotEmpty ? outcomeCategories[0] : null;
      selectedIncomeCategory.value = incomeCategories.isNotEmpty ? incomeCategories[0] : null;

      BillDetailInfo? detail = _bookkeepingContext?.logDetail;
      if (detail != null) {
        bookkeepingInfo.value = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == detail.bookkeepingNumber);
        selectedTab.value = detail.type == '2' ? 0 : 1;
        if (selectedTab.value == 0) {
          selectedOutcomeCategory.value = BookkeepingStore.to.outcomeCategories.firstWhereOrNull((el) => el.bookkeepingCategoryId == detail.categoryId);
          firstOutcomeAccount.value = BookkeepingStore.to.accountList.firstWhereOrNull((el) {
            return el.id == detail.accountId;
          });
          firstIncomeAccount.value = firstOutcomeAccount.value;
        } else {
          selectedIncomeCategory.value = BookkeepingStore.to.incomeCategories.firstWhereOrNull((el) => el.bookkeepingCategoryId == detail.categoryId);
          firstIncomeAccount.value = BookkeepingStore.to.accountList.firstWhereOrNull((el) {
            return el.id == detail.accountId;
          });
          firstOutcomeAccount.value = firstIncomeAccount.value;
        }

        selectedDay.value = _dateFormat.tryParse(detail.date ?? '') ?? DateTime.now();
        bookkeepingResult.value = (NumberFormat().tryParse(detail?.money ?? '') ?? 0.0).toDouble();
        textController.text = bookkeepingResult.value?.toStringAsFixed(2) ?? '';
        savingResult.value = selectedTab.value == 0 ? (detail.couponPrice != null ? NumberFormat().tryParse(detail.couponPrice!)?.toDouble() : null) : null;
        remarkStr.value = detail.memo ?? '';
        noNeed.value = detail.isNecessaryStatus == '2'; //_noNeed ? '2' : '1'
        isSavingInput.value = false;
      } else if (_bookkeepingContext?.selectedAccount?.isNotEmpty == true) {
        firstIncomeAccount.value = _bookkeepingContext?.selectedAccount?.first;
        firstOutcomeAccount.value = _bookkeepingContext?.selectedAccount?.first;
      } else {
        firstIncomeAccount.value = _bookkeepingContext?.incomeDefaultAccount?.first;
        firstOutcomeAccount.value = _bookkeepingContext?.outcomeDefaultAccount?.first;
      }

      selectedOutcomeAccount.value = firstOutcomeAccount.value;
      selectedIncomeAccount.value = firstIncomeAccount.value;
    }

    if (bookkeepingInfo.value == null) {
      if (UserStore.to.lastIndexBookkeepingNumber?.isNotEmpty == true) {
        bookkeepingInfo.value = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == UserStore.to.lastIndexBookkeepingNumber);
      }
    }
    if (bookkeepingInfo.value == null) {
      bookkeepingInfo.value = bookkeepingList.first;
    }
  }

  void onBookkeepingTap() {
    RouterHelper.router.pushNamed(Routes.chooseLedgarPath, extra: {
      'selected': bookkeepingInfo.value?.bookkeepingNumber?.isNotEmpty == true ? [bookkeepingInfo.value?.bookkeepingNumber ?? ''] : <String>[],
      'multiSelect': false
    }).then((value) {
      if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
        final bookkeepingList = BookkeepingStore.to.bookkeepingList;
        bookkeepingInfo.value = bookkeepingList.firstWhereOrNull((el) => el.bookkeepingNumber == value['selected'][0]);
      }
    });
  }

  void appendText(String append) {
    if (textNode.hasFocus) {
      String preText = textController.text;
      int start = textController.selection.baseOffset;
      int end = textController.selection.extentOffset;
      String newText = '';
      if (start != end) {
        newText = preText.replaceRange(start, end, append);
      } else {
        newText = preText.substring(0, start) + append + preText.substring(start, preText.length);
      }
      textController.value = TextEditingValue(text: newText, selection: TextSelection(baseOffset: start + 1, extentOffset: start + 1));
      textNode.unfocus();
    } else {
      textController.text += append;
    }
  }

  void deleteText() {
    String preText = textController.text;
    String newText = '';
    if (textNode.hasFocus) {
      int start = textController.selection.baseOffset;
      int end = textController.selection.extentOffset;
      if (start != end) {
        newText = preText.replaceRange(start, end, '');
      } else if (start == 0) {
        newText = preText;
      } else {
        newText = preText.substring(0, start - 1) + preText.substring(start, preText.length);
      }
    } else {
      if (preText.isNotEmpty) {
        newText = preText.substring(0, preText.length - 1);
      } else {
        newText = '';
      }
    }
    textController.value = TextEditingValue(text: newText);
    textNode.unfocus();
  }

  void confirm(bool needPop) {
    if (textController.text.isNotEmpty) {
      var [isSuccess, result] = calculate(textController.text);
      if (isSuccess) {
        if (isSavingInput.value) {
          savingResult.value = result;
        } else {
          bookkeepingResult.value = result;
        }
        doRequest(needPop: needPop);
      }
    } else {
      doRequest(needPop: needPop);
    }
  }

  List calculate(String userInput) {
    try {
      String input = userInput.replaceAll('×', '*');
      input = input.replaceAll('÷', '/');
      Parser p = Parser();
      Expression expression = p.parse(input);
      ContextModel contextModel = ContextModel();

      double eval = expression.evaluate(EvaluationType.REAL, contextModel);
      return [true, eval];
    } catch (e) {
      showToast('输入错误');
      return [false, null];
    }
  }

  Future<void> doRequest({bool? needPop = false}) async {
    if (bookkeepingInfo.value?.bookkeepingNumber?.isNotEmpty != true) {
      showToast('请先选择账本');
      return;
    }
    if ((selectedTab.value == 0 && selectedOutcomeCategory.value?.bookkeepingCategoryId == null) ||
        (selectedTab.value == 1 && selectedIncomeCategory.value?.bookkeepingCategoryId == null)) {
      showToast('请先选择分类');
      return;
    }
    var selectedAccount = selectedTab.value == 0 ? selectedOutcomeAccount.value : selectedIncomeAccount.value;
    if (selectedAccount?.id == null) {
      showToast('请先选择账户');
      return;
    }
    if (bookkeepingResult.value == null) {
      showToast('请先输入金额');
      return;
    }
    if (isSavingInput.value == true && savingResult.value == null) {
      showToast('请先输入节省金额');
      return;
    }
    Loading.show();
    try {
      var resp = await BookkeepingRepo.addBill(
          bookkeepingNumber: bookkeepingInfo.value!.bookkeepingNumber!,
          categoryId: selectedTab.value == 0 ? selectedOutcomeCategory.value!.bookkeepingCategoryId! : selectedIncomeCategory.value!.bookkeepingCategoryId!,
          action: selectedTab.value == 0 ? '2' : '1',
          money: bookkeepingResult.value!.toStringAsFixed(2),
          isSave: (savingResult.value != null && savingResult.value! > 0) ? '2' : '1',
          isSaveMoney: (savingResult.value != null && savingResult.value! > 0) ? savingResult.value?.toStringAsFixed(2) : null,
          isNecessaryStatus: noNeed.value ? '2' : '1',
          nowTime: _dateFormat.format(selectedDay.value),
          accountId: selectedAccount!.id!,
          memo: remarkStr.value,
          moneyLogId: logId);
      if (resp.code == 1) {
        showToast('添加流水成功');
        if (needPop == true) {
          RouterHelper.router.pop();
        } else {
          textController.text = '';
          isSavingInput.value = false;
          bookkeepingResult.value = null;
          savingResult.value = null;
          noNeed.value = false;
          remarkStr.value = '';
        }
      } else {
        showToast(resp.msg ?? '添加流水失败');
      }
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, maxFrames: 3);
      logger.e('addBill error $e');
      showToast('添加流水失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  void handleSavingTap() {
    if (isSavingInput.value == true) {
      if (textController.text.isNotEmpty) {
        var [isSuccess, result] = calculate(textController.text);
        if (isSuccess) {
          isSavingInput.value = false;
          savingResult.value = result;
          textController.text = bookkeepingResult.value!.toStringAsFixed(2);
        }
      } else {
        isSavingInput.value = false;
        savingResult.value = null;
        textController.text = bookkeepingResult.value!.toStringAsFixed(2);
      }
    } else {
      if (textController.text.isNotEmpty) {
        var [isSuccess, result] = calculate(textController.text);
        if (isSuccess) {
          isSavingInput.value = true;
          bookkeepingResult.value = result;
          textController.text = savingResult.value?.toStringAsFixed(2) ?? '';
        }
      } else {
        showToast('请先输入${selectedTab.value == 0 ? '支出' : '收入'}金额，再输入节省金额');
      }
    }
  }

  Future<void> setShortcutAccount(AccountModel account) async {
    try {
      final response = await BookkeepingRepo.setShortcutAccount(
        type: selectedTab.value == 0 ? 1 : 2,
        accountId: account.id.toString(),
      );
      if (response.code == 1) {
        showToast('设置快捷账户成功');
        getShortcutAccounts();
      } else {
        showToast(response.msg ?? '设置失败');
      }
    } catch (e) {
      showToast('设置失败: $e');
    }
  }

  Future<void> cancelShortcutAccount(AccountModel account) async {
    try {
      final response = await BookkeepingRepo.cancelShortcutAccount(
        type: selectedTab.value == 0 ? 1 : 2,
        accountId: account.id.toString(),
      );
      if (response.code == 1) {
        showToast('取消快捷账户成功');
        getShortcutAccounts();
      } else {
        showToast(response.msg ?? '取消失败');
      }
    } catch (e) {
      showToast('取消失败: $e');
    }
  }

  Future<void> getShortcutAccounts() async {
    try {
      final resp = await BookkeepingRepo.getShortcutAccounts();
      if (resp.code == 1) {
        shortcutIncomeAccounts.clear();
        shortcutIncomeAccounts.addAll(resp.data?.income ?? []);
        shortcutOutcomeAccounts.clear();
        shortcutOutcomeAccounts.addAll(resp.data?.outcome ?? []);
      } else {
        showToast(resp.msg ?? '获取快捷账户失败');
      }
    } catch (e) {
      showToast('获取快捷账户失败: $e');
    }
  }

  Future<void> saveShortcutOrder(List<AccountModel> newShortcuts) async {
    try {
      final List<String> accountIds = newShortcuts.map((account) => account.id.toString()).toList();
      final resp = await BookkeepingRepo.sortShortcutAccounts(
        type: selectedTab.value == 0 ? 1 : 2,
        accounts: accountIds.join(','),
      );
      if (resp.code == 1) {
        getShortcutAccounts();
      }
      // widget.onRefreshData();
    } catch (e) {
      showToast('保存排序失败: $e');
    }
  }

  Future<void> onCategorySelected(CategoryItem? item) async {
    if (item == null) {
      if (selectedTab.value == 0) {
        selectedOutcomeCategory.value = null;
        selectedOutcomeAccount.value = null;
      } else {
        selectedIncomeCategory.value = null;
        selectedIncomeAccount.value = null;
      }
      return;
    }
    if (logId != null || targetAccountId != null) {
      if (selectedTab.value == 0) {
        selectedOutcomeCategory.value = item;
      } else {
        selectedIncomeCategory.value = item;
      }
      return;
    }
    try {
      final resp = await BookkeepingRepo.getDefaultAccount(item.bookkeepingCategoryId!);
      if (resp.code == 1) {
        if (selectedTab.value == 0) {
          selectedOutcomeCategory.value = item;
          firstOutcomeAccount.value = resp.data?.firstOrNull;
          selectedOutcomeAccount.value = firstOutcomeAccount.value;
        } else {
          selectedIncomeCategory.value = item;
          firstIncomeAccount.value = resp.data?.firstOrNull;
          selectedIncomeAccount.value = firstIncomeAccount.value;
        }
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 3);
      showToast('获取分类默认账户失败: $e');
      logger.e('获取分类默认账户失败: $e');
    }
  }
}
