import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class ShortcutSettingMenu extends GetView<BookkeepingController> {
  const ShortcutSettingMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isShortcutManageMode.value) {
        // 管理模式：只显示快捷账户，可拖动排序
        return _buildManageMode();
      } else {
        // 普通模式：显示所有账户
        return _buildNormalMode();
      }
    });
  }

  Widget _buildManageMode() {
    List<AccountModel> shortcuts = controller.selectedTab.value == 0 ? controller.shortcutOutcomeAccounts : controller.shortcutIncomeAccounts;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(
          '快捷账户',
          trailingWidget: GestureDetector(
            onTap: () {
              // setState(() {
              controller.isShortcutManageMode.value = !controller.isShortcutManageMode.value;
              // });
            },
            child: Text(
              controller.isShortcutManageMode.value ? '完成' : '排序',
              style: const TextStyle(fontSize: 14, color: MColor.skin),
            ),
          ),
        ),
        const SizedBox(height: 12),
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: shortcuts.length,
          onReorder: (oldIndex, newIndex) {
            if (newIndex > oldIndex) {
              newIndex -= 1;
            }
            final item = shortcuts.removeAt(oldIndex);
            shortcuts.insert(newIndex, item);
            controller.saveShortcutOrder(shortcuts);
          },
          itemBuilder: (context, index) {
            final account = shortcuts[index];
            return _buildAccountTile(
              account,
              key: ValueKey(account.id),
              showTrailing: true,
              trailingWidget: const Icon(Icons.drag_handle, color: MColor.xFF999999),
            );
          },
        ),
      ],
    );
  }

  Widget _buildNormalMode() {
    return Obx(() {
      List<Widget> children = [];

      List<AccountModel> shortcuts = controller.selectedTab.value == 0 ? controller.shortcutOutcomeAccounts : controller.shortcutIncomeAccounts;

      // 快捷账户
      if (shortcuts.isNotEmpty) {
        // 其他账户
        children.add(_buildSectionTitle(
          controller.selectedTab.value == 0 ? '快捷支出账户' : '快捷收入账户',
          trailingWidget: GestureDetector(
            onTap: () {
              // setState(() {
              controller.isShortcutManageMode.value = !controller.isShortcutManageMode.value;
              // });
            },
            child: Text(
              controller.isShortcutManageMode.value ? '完成' : '排序',
              style: const TextStyle(fontSize: 14, color: MColor.skin),
            ),
          ),
        ));

        for (var account in shortcuts) {
          // if (widget.lastAccount == null || account.id != widget.lastAccount!.id) {
          children.add(_buildAccountTile(account, isShortcut: true));
          // }
        }
        children.add(const SizedBox(height: 8));

        // 分割线
        children.add(const Divider(color: MColor.xFFEEEEEE, height: 1));
        children.add(const SizedBox(height: 8));
      }

      final otherAccounts = BookkeepingStore.to.accountList.where((account) {
        final isShortcut = shortcuts.any((shortcut) => shortcut.id == account.id);
        return !isShortcut &&
            (controller.selectedTab.value == 0 && account.isSupportOutcome() || controller.selectedTab.value == 1 && account.isSupportIncome());
      }).toList();

      if (otherAccounts.isNotEmpty) {
        // 其他账户
        children.add(_buildSectionTitle('添加快捷'));

        for (var account in otherAccounts) {
          children.add(_buildAccountTile(account, isShortcut: false));
        }
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    });
  }

  Widget _buildSectionTitle(String title, {Widget? trailingWidget}) {
    return Container(
      child: ListTile(
          dense: true,
          visualDensity: VisualDensity.compact,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          title: Text(
            title,
            style: TextStyle(fontSize: 14, color: MColor.xFF999999),
          ),
          trailing: trailingWidget),
    );
  }

  Widget _buildAccountTile(
    AccountModel account, {
    Key? key,
    bool isShortcut = false,
    bool showTrailing = false,
    Widget? trailingWidget,
  }) {
    return Container(
      key: key,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        leading: Image.asset('assets/images/ic_card.png', width: 24, height: 24),
        title: Text(
          account.accountName ?? '账户',
          style: const TextStyle(fontSize: 14, color: MColor.xFF1B1C1A),
        ),
        trailing: showTrailing
            ? trailingWidget
            : isShortcut
                ? GestureDetector(
                    onTap: () => controller.cancelShortcutAccount(account),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: MColor.xFFFF7858,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '取消快捷',
                        style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
                      ),
                    ),
                  )
                : GestureDetector(
                    onTap: () => controller.setShortcutAccount(account),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: MColor.skin,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '添加快捷',
                        style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
                      ),
                    ),
                  ),
        onTap: () {
          // widget.onAccountSelected(account);
          RouterHelper.router.pop();
        },
      ),
    );
  }
}
