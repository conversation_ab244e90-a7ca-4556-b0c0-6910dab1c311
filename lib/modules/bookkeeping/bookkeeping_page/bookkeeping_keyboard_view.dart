import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:intl/intl.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/extensions.dart';
import 'package:qiazhun/tools/tools.dart';

class BookkeepingKeyboardView extends GetView<BookkeepingController> {
  const BookkeepingKeyboardView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _buildKeyboardRows(context),
    );
  }

  List<Widget> _buildKeyboardRows(BuildContext context) {
    return [
      // 第一行：运算符号
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_div.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('÷'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_mul.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('×'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_sub.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('-'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_add.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('+'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_back.png',
          iconWidth: 27,
          iconHeight: 20,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: controller.deleteText,
        ),
      ], marginTop: 0),

      // 第二行：1 2 3 4 再记
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          text: '1',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('1'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '2',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('2'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '3',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('3'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '4',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('4'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '再记',
          fontSize: 14,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: () => controller.doRequest(needPop: false),
        ),
      ], marginTop: 10),

      // 第三行：5 6 7 8 账户
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          text: '5',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('5'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '6',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('6'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '7',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('7'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '8',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('8'),
        ),
        _KeyboardItem(
          ratio: 1,
          widget: Obx(() {
            return FittedBox(
              fit: BoxFit.fill,
              child: Text(controller.bookkeepingInfo.value?.accountBookName ?? '账本',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 16,
                    height: 1,
                    color: MColor.skin,
                  )),
            );
          }),
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: controller.onBookkeepingTap,
        ),
      ], marginTop: 10),

      // 第四行：设置 9 . 0 确认
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_setting.png',
          widget: GestureDetector(
            onTap: () {
              showDatePicker(
                locale: const Locale('zh', 'CN'),
                context: context,
                firstDate: DateTime(2020, 1, 1),
                currentDate: controller.selectedDay.value,
                lastDate: DateTime.now().nextYear(),
              ).then((date) {
                if (date != null) {
                  // onDateSelected(date);
                  controller.selectedDay.value = date;
                }
              });
            },
            child: FittedBox(
              fit: BoxFit.fill,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                decoration: BoxDecoration(
                  color: MColor.xFFD7F5E6,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Image.asset('assets/images/ic_cal_calendar.png', width: 16, height: 16),
                    Icon(Icons.calendar_month, color: MColor.skin, size: 16),
                    const SizedBox(width: 2),
                    Obx(() {
                      return Text(
                        controller.selectedDay.value.isSameDay(DateTime.now()) ? '今天' : DateFormat('M月d日').format(controller.selectedDay.value),
                        style: const TextStyle(color: MColor.skin, fontSize: 12),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ),
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
        ),
        _KeyboardItem(
          ratio: 1,
          text: '9',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('9'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '0',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('0'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '.',
          fontSize: 24,
          bgColor: MColor.xFFD7F5E6,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('.'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '确认',
          fontSize: 16,
          fontColor: MColor.xFFFFFFFF,
          bgColor: MColor.xFFFF918D,
          borderColor: MColor.xFFED726E,
          marginRight: 0,
          onTap: () => controller.confirm(true),
        ),
      ], marginTop: 10),
    ];
  }

  Widget _buildKeyboardRow(List<_KeyboardItem> items, {required double marginTop}) {
    List<Widget> rowWidgets = [];

    for (var item in items) {
      rowWidgets.add(Expanded(
        flex: item.ratio,
        child: GestureDetector(
          onTap: item.onTap,
          child: Container(
            decoration: BoxDecoration(
              color: item.bgColor,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: item.borderColor, width: 1),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 2),
                  blurRadius: 0,
                  color: item.borderColor,
                ),
              ],
            ),
            height: 36,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Center(
              child: item.widget ??
                  (item.icon != null
                      ? Image.asset(
                          item.icon!,
                          width: item.iconWidth,
                          height: item.iconHeight,
                          fit: BoxFit.fill,
                        )
                      : FittedBox(
                          fit: BoxFit.fill,
                          child: Text(
                            item.text!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: item.fontSize!,
                              height: 1,
                              color: item.fontColor ?? item.borderColor,
                            ),
                          ),
                        )),
            ),
          ),
        ),
      ));

      if (item.marginRight > 0) {
        rowWidgets.add(SizedBox(width: item.marginRight));
      }
    }

    return Container(
      margin: EdgeInsets.only(top: marginTop),
      child: Row(children: rowWidgets),
    );
  }
}

class _KeyboardItem {
  final int ratio;
  final String? icon;
  final double? iconWidth;
  final double? iconHeight;
  final Color bgColor;
  final Color borderColor;
  final double marginRight;
  final String? text;
  final double? fontSize;
  final Color? fontColor;
  final Widget? widget;
  final VoidCallback? onTap;

  _KeyboardItem({
    required this.ratio,
    this.icon,
    this.iconWidth,
    this.iconHeight,
    required this.bgColor,
    required this.borderColor,
    required this.marginRight,
    this.text,
    this.fontSize,
    this.fontColor,
    this.widget,
    this.onTap,
  });
}
