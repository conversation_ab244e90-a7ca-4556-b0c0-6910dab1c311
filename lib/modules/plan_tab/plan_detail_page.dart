import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/plan_tab/plan_model.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/repo/plan_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/round_image.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/widgets/price_view.dart';

class PlanDetailPage extends StatefulWidget {
  final String planId;
  const PlanDetailPage(this.planId, {super.key});

  @override
  State<StatefulWidget> createState() => _PlanDetailState();
}

class _PlanDetailState extends State<PlanDetailPage> {
  PlanInfoResp? _resp = null;
  PlanInfo? _planInfo = null;
  List<PlanLogInfo> _transactions = [];
  bool _isDataReady = false;

  @override
  void initState() {
    _getData();
    super.initState();
  }

  Future<void> _getData() async {
    Loading.show();
    try {
      var resp = await PlanRepo.getPlanDetail(widget.planId);
      if (resp.code == 1) {
        _resp = resp.data;
        _planInfo = _resp?.detail;
        _transactions = _resp?.logs ?? [];
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 5);
      showToast(e.toString());
    } finally {
      setState(() {
        _isDataReady = true;
      });
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '计划详情',
      floatingActionButton: _planInfo == null
          ? null
          : IconButton(
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              style: const ButtonStyle(
                tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
              ),
              icon: Image.asset(
                'assets/images/ic_add.png',
                height: 50,
                width: 50,
              ),
              onPressed: () {
                RouterHelper.router.pushNamed(Routes.addSavingDialogPath, pathParameters: {'planId': '${_planInfo!.id}'}).then((value) {
                  _getData();
                });
              },
            ),
      actions: [
        IconButton(
            onPressed: () {
              RouterHelper.router.pushNamed(Routes.planEditPath, pathParameters: {'planId': widget.planId}).then((value) {
                _getData();
              });
            },
            icon: Image.asset(
              'assets/images/ic_setting_2.png',
              width: 24,
              height: 24,
              fit: BoxFit.fill,
            )),
      ],
      body: Builder(builder: (context) {
        if (_planInfo == null && !_isDataReady) {
          return const LoadingView();
        } else if (_planInfo == null && _isDataReady) {
          return EmptyView();
        }
        double imageWidth = MediaQuery.of(context).size.width - 28;
        List<Widget> widgets = [
          CachedNetworkImage(imageUrl: getImageUrl(_planInfo?.backgroundImage), height: 165, width: imageWidth, fit: BoxFit.fill),
          const SizedBox(
            height: 14,
          ),
          _planItemView,
          const SizedBox(
            height: 14,
          ),
          _sectionHeader
        ];
        for (var trans in _transactions) {
          widgets.add(_planLogView(
            trans,
          ));
        }
        return ListView(
          children: widgets,
          padding: EdgeInsets.all(0),
        );
      }),
    );
  }

  Widget _planLogView(PlanLogInfo log) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14, vertical: 6),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(14), color: MColor.xFFFFFFFF),
      child: ListTile(
        dense: true,
        title: Text(
          log.memo,
          style: TextStyle(height: 1.4, fontSize: 14, fontWeight: FontWeight.bold, color: MColor.xFF1B1C1A),
        ),
        subtitle: Text(
          log.createtime,
          style: TextStyle(height: 1.4, fontSize: 12, fontWeight: FontWeight.normal, color: MColor.xFF8C8C8C),
        ),
        trailing: PriceView(
          price: PriceInfo.parsePrice(log.price ?? '0.00'),
          integerFontSize: 12,
          fractionalFontSize: 10,
          textColor: MColor.xFF8C8C8C,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  Widget get _planItemView {
    if (_planInfo == null) {
      return const SizedBox();
    }
    double barWidth = MediaQuery.of(context).size.width - 28 - 28;
    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: MColor.xFFFFFFFF),
        padding: EdgeInsets.all(14),
        margin: EdgeInsets.symmetric(horizontal: 14),
        child: Column(
          children: [
            Builder(builder: (context) {
              return Container(
                // color: Colors.yellow,
                child: Row(
                  children: [
                    if (_planInfo!.icon?.isNotEmpty == true) RoundImage(imageUrl: _planInfo!.icon!, size: 32, radius: 16),
                    const SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: Text(
                        _planInfo!.name,
                        style: TextStyle(fontSize: 14, height: 1.4, color: MColor.xFF1B1C1A),
                      ),
                    ),
                  ],
                ),
              );
            }),
            const SizedBox(
              height: 14,
            ),
            SizedBox(
                height: 19,
                child: Stack(
                  children: [
                    Container(
                      width: barWidth,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          gradient: LinearGradient(
                            // begin: Alignment.topRight,
                            // end: Alignment.bottomLeft,
                            stops: [
                              0.0,
                              ((double.tryParse(_planInfo!.proportion ?? '') ?? 0) / 100),
                              ((double.tryParse(_planInfo!.proportion ?? '') ?? 0) / 100),
                              1.0
                            ],
                            begin: const FractionalOffset(0.0, 0.0),
                            end: const FractionalOffset(1.0, 0.0),
                            colors: [
                              MColor.skin,
                              MColor.skin,
                              MColor.skin.withOpacity(0.2),
                              MColor.skin.withOpacity(0.2),
                            ],
                          )),
                    ),
                  ],
                )),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '攒钱目标',
                  style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF999999),
                ),
                const Spacer(),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PriceView(
                      price: PriceInfo.parsePrice(_planInfo!.accumulatedAmount ?? '0.00'),
                      integerFontSize: 12,
                      fractionalFontSize: 10,
                      textColor: MColor.skin,
                      showSymbol: false,
                    ),
                    Text(
                      '/',
                      style: TextStyle(fontSize: 12, height: 1.4, color: MColor.skin),
                    ),
                    PriceView(
                      price: PriceInfo.parsePrice(_planInfo!.requiredAmount ?? '0.00'),
                      integerFontSize: 12,
                      fractionalFontSize: 10,
                      textColor: MColor.skin,
                      showSymbol: false,
                    ),
                  ],
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget get _sectionHeader {
    if (_planInfo == null) {
      return const SizedBox();
    }
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: MColor.xFFFFFFFF),
      padding: EdgeInsets.all(14),
      margin: EdgeInsets.symmetric(horizontal: 14),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 13,
                width: 3,
                decoration: BoxDecoration(color: MColor.xFFFFD180, borderRadius: BorderRadius.circular(2)),
              ),
              const SizedBox(
                width: 7,
              ),
              Text(
                '计划说明',
                style: TextStyle(color: MColor.xFF1B1C1A, fontSize: 15, height: 1.4),
              ),
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                _planInfo?.memo ?? '',
                style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF777777),
              ))
            ],
          )
        ],
      ),
    );
  }
}
