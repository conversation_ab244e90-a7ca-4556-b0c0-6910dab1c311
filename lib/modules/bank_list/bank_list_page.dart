import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bank_list/bank_list_bloc.dart';
import 'package:qiazhun/modules/bank_list/bank_list_event.dart';
import 'package:qiazhun/modules/bank_list/bank_list_state.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BankListPage extends StatelessWidget {
  final bool onlyBank;
  const BankListPage({required this.onlyBank, super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BankListBloc()..add(LoadBankListEvent(onlyBank: onlyBank)),
      child: BankListView(onlyBank: onlyBank),
    );
  }
}

class BankListView extends StatelessWidget {
  final bool onlyBank;
  const BankListView({required this.onlyBank, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          scrolledUnderElevation: 0,
          centerTitle: true,
          leading: IconButton(
            icon: Image.asset(
              'assets/images/ic_back.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              RouterHelper.router.pop();
            },
          ),
          title: Image.asset(
            'assets/images/icon_title.png',
            width: 129,
            height: 30,
          )),
      body: SafeArea(
        top: false,
        child: Stack(
          children: [
            Container(
              color: const Color(0xFFF5F5F5),
            ),
            Positioned(
              child: Container(
                height: 203,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                      colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                ),
              ),
            ),
            Positioned.fill(
              top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
              child: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
                padding: const EdgeInsets.all(14),
                margin: const EdgeInsets.symmetric(horizontal: 14),
                child: BlocConsumer<BankListBloc, BankListState>(
                  listener: (context, state) {
                    if (state is BankListLoading) {
                      Loading.show();
                    } else {
                      Loading.dismiss();
                    }

                    if (state is BankListError) {
                      showToast(state.message);
                    }
                  },
                  builder: (context, state) {
                    if (state is BankListLoaded) {
                      return GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          mainAxisSpacing: 14,
                          crossAxisSpacing: 14,
                          childAspectRatio: 1.0,
                        ),
                        itemBuilder: (context, index) {
                          return _bankItemView(state.bankList[index]);
                        },
                        itemCount: state.bankList.length,
                      );
                    }

                    // Show empty container for loading and error states
                    return const SizedBox.shrink();
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _bankItemView(BankItem item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        RouterHelper.router.pop(item);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RoundImage(imageUrl: getImageUrl(item.bankImage), radius: 16, size: 32),
            const SizedBox(height: 8),
            Text(
              item.bankName ?? '',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
