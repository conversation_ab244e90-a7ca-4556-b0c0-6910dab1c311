import 'package:equatable/equatable.dart';

/// Base class for all BankList events
abstract class BankListEvent extends Equatable {
  const BankListEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load the bank list
class LoadBankListEvent extends BankListEvent {
  final bool onlyBank;

  const LoadBankListEvent({required this.onlyBank});

  @override
  List<Object?> get props => [onlyBank];
}

/// Event to refresh the bank list
class RefreshBankListEvent extends BankListEvent {
  final bool onlyBank;

  const RefreshBankListEvent({required this.onlyBank});

  @override
  List<Object?> get props => [onlyBank];
}
