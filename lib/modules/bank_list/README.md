# BankList Bloc Implementation

This module has been migrated from GetX state management to Bloc pattern.

## Files Structure

- `bank_list_event.dart` - Defines all events that can be triggered in the bank list page
- `bank_list_state.dart` - Defines all possible states of the bank list page
- `bank_list_bloc.dart` - Contains the business logic and handles state transitions
- `bank_list_page.dart` - The UI component that uses BlocProvider and BlocConsumer

## Usage

The BankListPage is now a StatelessWidget that provides a BlocProvider:

```dart
class BankListPage extends StatelessWidget {
  final bool onlyBank;
  const BankListPage({required this.onlyBank, super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BankListBloc()..add(LoadBankListEvent(onlyBank: onlyBank)),
      child: BankListView(onlyBank: onlyBank),
    );
  }
}
```

## Events

- `LoadBankListEvent` - Triggers loading of bank list data
- `RefreshBankListEvent` - Triggers refreshing of bank list data

## States

- `BankListInitial` - Initial state when page is first created
- `BankListLoading` - State when data is being loaded
- `BankListLoaded` - State when data is successfully loaded
- `BankListError` - State when an error occurs during loading

## Benefits of Bloc Migration

1. **Separation of Concerns**: Business logic is separated from UI
2. **Testability**: Bloc can be easily unit tested
3. **Predictable State Management**: Clear state transitions
4. **Reactive Programming**: UI automatically updates when state changes
5. **Better Error Handling**: Centralized error handling in the bloc

## Testing

Unit tests are available in `test/modules/bank_list/bank_list_bloc_test.dart` to verify the bloc behavior.
