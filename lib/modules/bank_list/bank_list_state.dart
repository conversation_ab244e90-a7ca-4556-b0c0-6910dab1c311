import 'package:equatable/equatable.dart';
import 'package:qiazhun/modules/account/account_model.dart';

/// Base class for all BankList states
abstract class BankListState extends Equatable {
  const BankListState();

  @override
  List<Object?> get props => [];
}

/// Initial state when the page is first loaded
class BankListInitial extends BankListState {
  const BankListInitial();
}

/// State when loading bank list data
class BankListLoading extends BankListState {
  const BankListLoading();
}

/// State when bank list data is successfully loaded
class BankListLoaded extends BankListState {
  final List<BankItem> bankList;

  const BankListLoaded({required this.bankList});

  @override
  List<Object?> get props => [bankList];
}

/// State when there's an error loading bank list data
class BankListError extends BankListState {
  final String message;

  const BankListError({required this.message});

  @override
  List<Object?> get props => [message];
}
