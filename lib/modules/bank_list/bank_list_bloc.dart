import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/bank_list/bank_list_event.dart';
import 'package:qiazhun/modules/bank_list/bank_list_state.dart';

/// Bloc for managing bank list state and business logic
class BankListBloc extends Bloc<BankListEvent, BankListState> {
  BankListBloc() : super(const BankListInitial()) {
    on<LoadBankListEvent>(_onLoadBankList);
    on<RefreshBankListEvent>(_onRefreshBankList);
  }

  /// Handle loading bank list event
  Future<void> _onLoadBankList(
    LoadBankListEvent event,
    Emitter<BankListState> emit,
  ) async {
    emit(const BankListLoading());
    await _loadBankList(event.onlyBank, emit);
  }

  /// Handle refreshing bank list event
  Future<void> _onRefreshBankList(
    RefreshBankListEvent event,
    Emitter<BankListState> emit,
  ) async {
    emit(const BankListLoading());
    await _loadBankList(event.onlyBank, emit);
  }

  /// Common method to load bank list data
  Future<void> _loadBankList(
    bool onlyBank,
    Emitter<BankListState> emit,
  ) async {
    try {
      final resp = await AccountRepo.getBankList();
      
      if (resp.code == 1) {
        final bankList = (resp.data ?? [])
            .where((bank) => !(bank.hiddenType != '1' && onlyBank))
            .toList();
        
        emit(BankListLoaded(bankList: bankList));
      } else {
        emit(BankListError(message: resp.msg ?? '获取机构列表失败'));
      }
    } catch (e) {
      emit(BankListError(message: '获取机构列表失败 $e'));
    }
  }
}
