import 'package:json_annotation/json_annotation.dart';

part 'account_model.g.dart';

@JsonSerializable()
class AccountListResp extends Object {
  @Json<PERSON>ey(name: 'total')
  int total;

  @Json<PERSON><PERSON>(name: 'per_page')
  String perPage;

  @J<PERSON><PERSON><PERSON>(name: 'current_page')
  int currentPage;

  @Json<PERSON>ey(name: 'last_page')
  int lastPage;

  @Json<PERSON>ey(name: 'data')
  List<AccountModel> data;

  AccountListResp(
    this.total,
    this.perPage,
    this.currentPage,
    this.lastPage,
    this.data,
  );

  factory AccountListResp.fromJson(Map<String, dynamic> srcJson) => _$AccountListRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountListRespToJson(this);
}

@JsonSerializable()
class AccountGroupModel extends Object {
  @JsonKey(name: 'id')
  int? id;

  @J<PERSON><PERSON><PERSON>(name: 'user_id')
  int? userId;

  @J<PERSON><PERSON><PERSON>(name: 'name')
  String? name;

  @JsonKey(name: 'weigh')
  int? weigh;

  @Json<PERSON><PERSON>(name: 'totalAmount')
  String? totalAmount;
  @JsonKey(name: 'createtime')
  String? createtime;

  @JsonKey(name: 'type')
  int? type;

  @JsonKey(name: 'accounts')
  List<AccountModel>? accounts;

  AccountGroupModel(
    this.id,
    this.userId,
    this.name,
    this.weigh,
    this.createtime,
    this.type,
    this.accounts,
    this.totalAmount,
  );

  factory AccountGroupModel.fromJson(Map<String, dynamic> srcJson) => _$AccountGroupModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountGroupModelToJson(this);

  // Helper method to check if group has accounts
  bool get hasAccounts => accounts?.isNotEmpty == true;

  // Helper method to get account count
  int get accountCount => accounts?.length ?? 0;
}

@JsonSerializable()
class AccountModel extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'cardType')
  String? cardType;
  @JsonKey(name: 'subType')
  String? subType;

  @JsonKey(name: 'isJoinTotal')
  dynamic isJoinTotal;

  @JsonKey(name: 'accountIcon')
  String? accountIcon;

  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'balance')
  String? balance;

  @JsonKey(name: 'memo')
  String? memo;

  @JsonKey(name: 'bankName')
  String? bankName;

  @JsonKey(name: 'bankId')
  int? bankId;

  @JsonKey(name: 'bankIcon')
  String? bankIcon;

  @JsonKey(name: 'amount')
  String? amount;
  @JsonKey(name: 'billAmount')
  String? billAmount;

  @JsonKey(name: 'notBilledAmount')
  String? notBilledAmount;

  @JsonKey(name: 'availableAmount')
  String? availableAmount;

  @JsonKey(name: 'consumptionAmount')
  String? consumptionAmount;

  @JsonKey(name: 'annualFeeDate')
  String? annualFeeDate;

  @JsonKey(name: 'billDate')
  String? billDate;
  @JsonKey(name: 'dueDate')
  String? dueDate;
  @JsonKey(name: 'lastSelectedIncome')
  int? lastSelectedIncome;
  @JsonKey(name: 'lastSelectedPay')
  int? lastSelectedPay;

  @JsonKey(name: 'repaymentDate')
  String? repaymentDate;
  @JsonKey(name: 'proportion')
  String? proportion;

  @JsonKey(name: 'otherAccountInfo')
  dynamic otherAccountInfo;
  @JsonKey(name: 'transterAccountId')
  dynamic transterAccountId;

  @JsonKey(name: 'exemptionConditions')
  AnnualFeeCondition? annualFeeCondition;
  @JsonKey(name: 'promotionConditions')
  List<PromotionCondition>? promotions;
  @JsonKey(name: 'selectedStatus')
  int? selectedStatus;
  @JsonKey(name: 'hiddenType')
  dynamic hiddenType;
  AccountModel(
      this.id,
      this.cardType,
      this.subType,
      this.isJoinTotal,
      this.accountIcon,
      this.accountName,
      this.balance,
      this.memo,
      this.bankName,
      this.bankId,
      this.bankIcon,
      this.amount,
      this.billAmount,
      this.notBilledAmount,
      this.availableAmount,
      this.consumptionAmount,
      this.annualFeeDate,
      this.billDate,
      this.repaymentDate,
      this.dueDate,
      this.proportion,
      this.otherAccountInfo,
      this.annualFeeCondition,
      this.selectedStatus,
      this.lastSelectedIncome,
      this.lastSelectedPay,
      this.transterAccountId,
      this.hiddenType,
      this.promotions);

  factory AccountModel.fromJson(Map<String, dynamic> srcJson) => _$AccountModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountModelToJson(this);

  String getShortDesc() {
    if (cardType == '1' || cardType == '2') {
      return '$bankName ($accountName)';
    } else if (cardType == '3') {
      if (subType == '1') {
        return '微信账号 $accountName';
      } else if (subType == '2') {
        return '支付宝账号 $accountName';
      } else if (subType == '3') {
        return accountName ?? '';
      }
    } else if (cardType == '4') {
      return '现金账户 $accountName';
    } else if (cardType == '5') {
      return '投资账户 $accountName';
    } else if (cardType == '6') {
      return accountName ?? '';
    } else if (cardType == '7') {
      return accountName ?? '';
    } else if (cardType == '8') {
      return accountName ?? '';
    }
    return '';
  }

  String getIcon() {
    if (cardType == '1' || cardType == '2') {
      return bankIcon ?? accountIcon ?? '';
    }
    return accountIcon ?? '';
  }

  bool isSupportOutcome() {
    return cardType == '1' || cardType == '2' || cardType == '3' || cardType == '4';
  }

  bool isSupportIncome() {
    return cardType == '1' || cardType == '3' || cardType == '4';
  }
}

@JsonSerializable()
class BankItem extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'bankName')
  String? bankName;

  @JsonKey(name: 'bankImage')
  String? bankImage;

  @JsonKey(name: 'bankColour')
  String? bankColour;

  @JsonKey(name: 'hiddenType')
  String? hiddenType;

  BankItem(
    this.id,
    this.bankName,
    this.bankImage,
    this.bankColour,
    this.hiddenType,
  );

  factory BankItem.fromJson(Map<String, dynamic> srcJson) => _$BankItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$BankItemToJson(this);
}

@JsonSerializable()
class AnnualFeeCondition {
  @JsonKey(name: 'type')
  String? conditionType; //1 刷卡次数 2 年消费金额
  @JsonKey(name: 'count')
  String? count; //1 刷卡次数 对应的次数字段
  @JsonKey(name: 'time')
  String? countTimeUnit; //1 日 2 周 3 月 4 年
  @JsonKey(name: 'price')
  String? amount; // 2消费金额 对应的金额

  AnnualFeeCondition(this.conditionType, this.count, this.countTimeUnit, this.amount);
  factory AnnualFeeCondition.fromJson(Map<String, dynamic> srcJson) => _$AnnualFeeConditionFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AnnualFeeConditionToJson(this);
}

@JsonSerializable()
class PromotionCondition {
  @JsonKey(name: 'type')
  String? conditionType; //1 刷卡次数 2 消费金额 3 消费达标次数
  @JsonKey(name: 'name')
  String promotionName;

  @JsonKey(name: 'count')
  String? count; //1 刷卡次数 或者 3 消费达标次数 对应的次数字段
  @JsonKey(name: 'time')
  String? countTimeUnit; //1 日 2 周 3 月 4 年

  @JsonKey(name: 'price')
  String? amount; // 2消费金额 对应的金额

  PromotionCondition(this.conditionType, this.promotionName, this.count, this.countTimeUnit, this.amount);
  factory PromotionCondition.fromJson(Map<String, dynamic> srcJson) => _$PromotionConditionFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PromotionConditionToJson(this);
}

@JsonSerializable()
class RepaymentDiaryResp extends Object {
  @JsonKey(name: 'isRepayment')
  List<RepayDiaryModel>? isRepayment;
  @JsonKey(name: 'noRepayment')
  List<RepayDiaryModel>? noRepayment;
  RepaymentDiaryResp(this.isRepayment, this.noRepayment);

  factory RepaymentDiaryResp.fromJson(Map<String, dynamic> srcJson) => _$RepaymentDiaryRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$RepaymentDiaryRespToJson(this);
}

@JsonSerializable()
class RepayDiaryModel extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'bankName')
  String? bankName;

  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'issuedBill')
  String? issuedBill;

  @JsonKey(name: 'repaymentDate')
  String? repaymentDate;

  @JsonKey(name: 'accountIcon')
  String? accountIcon;

  @JsonKey(name: 'bankIcon')
  String? bankIcon;

  @JsonKey(name: 'bankColour')
  String? bankColour;

  @JsonKey(name: 'residue')
  dynamic residue;

  @JsonKey(name: 'overdue')
  String? overdue;

  RepayDiaryModel(
    this.id,
    this.bankName,
    this.cardNo,
    this.issuedBill,
    this.repaymentDate,
    this.accountIcon,
    this.bankIcon,
    this.bankColour,
    this.residue,
    this.overdue,
  );

  factory RepayDiaryModel.fromJson(Map<String, dynamic> srcJson) => _$RepayDiaryModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$RepayDiaryModelToJson(this);
}

@JsonSerializable()
class AnnualFeeResp {
  @JsonKey(name: 'noAnnual')
  List<AnnualFeePlanModel>? noAnnual;
  @JsonKey(name: 'isAnnual')
  List<AnnualFeePlanModel>? annual;

  AnnualFeeResp(this.noAnnual, this.annual);

  factory AnnualFeeResp.fromJson(Map<String, dynamic> srcJson) => _$AnnualFeeRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AnnualFeeRespToJson(this);
}

@JsonSerializable()
class AnnualFeePlanModel extends Object {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'bankId')
  int? bankId;

  @JsonKey(name: 'bankName')
  String? bankName;
  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'annualFeeDate')
  String? annualFeeDate;

  @JsonKey(name: 'annualPrice')
  String? annualPrice;

  @JsonKey(name: 'exemptionConditions')
  AnnualFeeCondition? exemptionConditions;

  @JsonKey(name: 'bankIcon')
  String? bankIcon;

  @JsonKey(name: 'bankColour')
  String? bankColour;

  @JsonKey(name: 'spendPriceTotal')
  String? spendPriceTotal;

  @JsonKey(name: 'spendPriceCount')
  String? spendPriceCount;
  @JsonKey(name: 'annualStatus')
  String? annualStatus;
  @JsonKey(name: 'annualMemo')
  String? annualMemo;
  @JsonKey(name: 'daysToAnnualFee')
  dynamic daysToAnnualFee;

  @JsonKey(name: 'days', defaultValue: 0)
  int? days;

  AnnualFeePlanModel(this.id, this.bankId, this.bankName, this.cardNo, this.annualFeeDate, this.annualPrice, this.exemptionConditions, this.bankIcon,
      this.bankColour, this.spendPriceTotal, this.spendPriceCount, this.annualMemo, this.days, this.annualStatus, this.daysToAnnualFee);

  factory AnnualFeePlanModel.fromJson(Map<String, dynamic> srcJson) => _$AnnualFeePlanModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AnnualFeePlanModelToJson(this);
}

@JsonSerializable()
class ConsumptionGuideModel extends Object {
  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'isUse')
  String? isUse;

  @JsonKey(name: 'billDate')
  String? billDate;

  @JsonKey(name: 'bank_id')
  int? bankId;

  @JsonKey(name: 'bankName')
  String? bankName;

  @JsonKey(name: 'cardNo')
  String? cardNo;

  @JsonKey(name: 'issuedBill')
  String? issuedBill;

  @JsonKey(name: 'repaymentDate')
  String? repaymentDate;

  @JsonKey(name: 'accountIcon')
  String? accountIcon;

  @JsonKey(name: 'bankIcon')
  String? bankIcon;

  @JsonKey(name: 'bankColour')
  String? bankColour;

  @JsonKey(name: 'residue')
  String? residue;
  @JsonKey(name: 'freePeriod')
  String? freePeriod;
  @JsonKey(name: 'availableAmount')
  String? availableAmount;

  ConsumptionGuideModel(
    this.id,
    this.isUse,
    this.billDate,
    this.bankId,
    this.bankName,
    this.cardNo,
    this.issuedBill,
    this.repaymentDate,
    this.accountIcon,
    this.bankIcon,
    this.bankColour,
    this.residue,
    this.freePeriod,
    this.availableAmount,
  );

  factory ConsumptionGuideModel.fromJson(Map<String, dynamic> srcJson) => _$ConsumptionGuideModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ConsumptionGuideModelToJson(this);
}

@JsonSerializable()
class UserAssetsModel {
  @JsonKey(name: 'assets')
  dynamic assets;

  @JsonKey(name: 'list')
  List<AccountModel>? list;

  UserAssetsModel(
    this.assets,
    this.list,
  );

  factory UserAssetsModel.fromJson(Map<String, dynamic> srcJson) => _$UserAssetsModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$UserAssetsModelToJson(this);
}

@JsonSerializable()
class AccountDetailAndLogResp extends Object {
  @JsonKey(name: 'list')
  List<AccountDateLog> list;

  @JsonKey(name: 'detail')
  AccountModel detail;

  AccountDetailAndLogResp(
    this.list,
    this.detail,
  );

  factory AccountDetailAndLogResp.fromJson(Map<String, dynamic> srcJson) => _$AccountDetailAndLogRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountDetailAndLogRespToJson(this);
}

@JsonSerializable()
class AccountDateLog extends Object {
  @JsonKey(name: 'date')
  String date;

  @JsonKey(name: 'income')
  String income;

  @JsonKey(name: 'expense')
  String expense;

  @JsonKey(name: 'items')
  List<AccountDateLogItem> items;

  AccountDateLog(
    this.date,
    this.income,
    this.expense,
    this.items,
  );

  factory AccountDateLog.fromJson(Map<String, dynamic> srcJson) => _$AccountDateLogFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountDateLogToJson(this);
}

@JsonSerializable()
class AccountDateLogItem extends Object {
  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'categoryName')
  String categoryName;

  @JsonKey(name: 'bookkeepingName')
  String bookkeepingName;

  @JsonKey(name: 'categoryIcon')
  String categoryIcon;

  @JsonKey(name: 'money')
  String money;

  @JsonKey(name: 'type')
  String type;

  @JsonKey(name: 'coupon_price')
  String couponPrice;

  @JsonKey(name: 'memo')
  String memo;

  @JsonKey(name: 'createtime')
  String createtime;

  AccountDateLogItem(
    this.id,
    this.categoryName,
    this.bookkeepingName,
    this.categoryIcon,
    this.money,
    this.type,
    this.couponPrice,
    this.memo,
    this.createtime,
  );

  factory AccountDateLogItem.fromJson(Map<String, dynamic> srcJson) => _$AccountDateLogItemFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AccountDateLogItemToJson(this);
}

@JsonSerializable()
class WoolHelperResp extends Object {
  List<WoolHelperData>? used;
  List<WoolHelperData>? unused;
  WoolHelperResp(this.used, this.unused);

  factory WoolHelperResp.fromJson(Map<String, dynamic> srcJson) => _$WoolHelperRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$WoolHelperRespToJson(this);
}

@JsonSerializable()
class WoolHelperData extends Object {
  @JsonKey(name: 'cardId')
  dynamic cardId;

  @JsonKey(name: 'bankName')
  String? bankName;
  @JsonKey(name: 'cardType')
  String? cardType;

  @JsonKey(name: 'accountName')
  String? accountName;

  @JsonKey(name: 'wools')
  List<Wools>? wools;

  WoolHelperData(
    this.cardId,
    this.bankName,
    this.cardType,
    this.accountName,
    this.wools,
  );

  factory WoolHelperData.fromJson(Map<String, dynamic> srcJson) => _$WoolHelperDataFromJson(srcJson);

  Map<String, dynamic> toJson() => _$WoolHelperDataToJson(this);
}

@JsonSerializable()
class Wools extends Object {
  @JsonKey(name: 'id')
  dynamic id;

  @JsonKey(name: 'bankIcon')
  String? bankIcon;

  @JsonKey(name: 'bankColour')
  String? bankColour;

  @JsonKey(name: 'name')
  String? name;

  @JsonKey(name: 'status')
  String? status;

  @JsonKey(name: 'progress')
  Progress? progress;

  @JsonKey(name: 'is_qualified')
  bool? isQualified;

  @JsonKey(name: 'content')
  Content? content;

  Wools(
    this.id,
    this.bankIcon,
    this.bankColour,
    this.name,
    this.status,
    this.progress,
    this.isQualified,
    this.content,
  );

  factory Wools.fromJson(Map<String, dynamic> srcJson) => _$WoolsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$WoolsToJson(this);
}

@JsonSerializable()
class Progress extends Object {
  @JsonKey(name: 'current')
  String? current;

  @JsonKey(name: 'target')
  String? target;

  @JsonKey(name: 'percent')
  String? percent;

  @JsonKey(name: 'unit')
  String? unit;

  @JsonKey(name: 'type')
  String? type;

  @JsonKey(name: 'type_text')
  String? typeText;

  @JsonKey(name: 'description')
  String? description;

  @JsonKey(name: 'qualified')
  bool? qualified;

  Progress(
    this.current,
    this.target,
    this.percent,
    this.unit,
    this.type,
    this.typeText,
    this.description,
    this.qualified,
  );

  factory Progress.fromJson(Map<String, dynamic> srcJson) => _$ProgressFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ProgressToJson(this);
}

@JsonSerializable()
class Content extends Object {
  @JsonKey(name: 'type')
  String? type;

  @JsonKey(name: 'name')
  String? name;

  @JsonKey(name: 'count')
  String? count;

  @JsonKey(name: 'time')
  String? time;

  @JsonKey(name: 'price')
  String? price;

  Content(
    this.type,
    this.name,
    this.count,
    this.time,
    this.price,
  );

  factory Content.fromJson(Map<String, dynamic> srcJson) => _$ContentFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ContentToJson(this);
}
