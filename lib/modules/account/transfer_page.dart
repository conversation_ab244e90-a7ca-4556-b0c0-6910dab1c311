import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/page_container_view.dart';
import 'package:qiazhun/widgets/price_view.dart';

class TransferPage extends StatefulWidget {
  final String fromAccountId;
  const TransferPage({required this.fromAccountId, super.key});

  @override
  State<StatefulWidget> createState() => _TransferPageState();
}

class _TransferPageState extends State<TransferPage> {
  bool _isLoading = true;
  AccountModel? _fromAccount;
  AccountModel? _toAccount;
  List<AccountModel> _availableAccounts = [];
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _feeController = TextEditingController();
  final TextEditingController _memoController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _feeController.dispose();
    _memoController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load from account details
      // final fromAccountResponse = await AccountRepo.getAccountDetailAndLog(widget.fromAccountId);
      // if (fromAccountResponse.code == 1 && fromAccountResponse.data != null) {
      //   _fromAccount = fromAccountResponse.data!.detail;
      // }

      // Load all accounts for selection
      final accountsResponse = await BookkeepingRepo.getAccountList(page: 1, pageCount: 100);
      if (accountsResponse.code == 1 && accountsResponse.data != null) {
        _fromAccount = accountsResponse.data!.where((account) => account.id.toString() == widget.fromAccountId).toList().firstOrNull;
        _availableAccounts = accountsResponse.data!.where((account) => account.id.toString() != widget.fromAccountId).toList();
        _toAccount = accountsResponse.data!.where((account) => account.id.toString() == _fromAccount?.transterAccountId.toString()).toList().firstOrNull;
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      _isLoading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(title: '转账', body: _isLoading ? _buildLoadingView() : _buildContent());
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        color: MColor.skin,
      ),
    );
  }

  Widget _buildContent() {
    if (_fromAccount == null) {
      return const Center(
        child: EmptyView(),
      );
    }

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTransferFlow(),
                const SizedBox(height: 20),
                _buildAmountSection(),
                const SizedBox(height: 20),
                _buildFeeSection(),
                const SizedBox(height: 20),
                _buildMemoSection(),
              ],
            ),
          ),
        ),
        _buildBottomButton(),
      ],
    );
  }

  Widget _buildTransferFlow() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // From Account
          _buildAccountCard(_fromAccount!, '转出账户'),
          const SizedBox(height: 20),
          // Transfer Arrow
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: MColor.skin.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.arrow_downward,
              color: MColor.skin,
              size: 24,
            ),
          ),
          const SizedBox(height: 20),
          // To Account
          _toAccount != null ? _buildAccountCard(_toAccount!, '转入账户') : _buildSelectAccountCard(),
        ],
      ),
    );
  }

  Widget _buildAccountCard(AccountModel account, String title) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: MColor.xFF777777,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: MColor.skin.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  account.cardType == '2' ? Icons.credit_card : Icons.account_balance_wallet,
                  color: MColor.skin,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account.accountName ?? '',
                      style: const TextStyle(
                        color: MColor.xFF1B1C1A,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          account.cardType == '2' ? '可用额度: ' : '余额: ',
                          style: const TextStyle(
                            color: MColor.xFF999999,
                            fontSize: 12,
                          ),
                        ),
                        PriceView(
                          price: PriceInfo.parsePrice(account.cardType == '2' ? (account.availableAmount ?? '0.00') : (account.balance ?? '0.00')),
                          integerFontSize: 12,
                          fractionalFontSize: 10,
                          textColor: MColor.xFF999999,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (title == '转入账户') ...[
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _toAccount = null;
                    });
                  },
                  child: const Icon(
                    Icons.close,
                    color: MColor.xFF999999,
                    size: 20,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectAccountCard() {
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(
          Routes.accountListPath,
          extra: {
            'selectMode': true,
            'limitAccount': '9',
          },
        ).then((value) {
          if (value != null && value is Map<String, dynamic> && value.containsKey('selected')) {
            AccountModel repayment = value['selected'] as AccountModel;
            setState(() {
              _toAccount = repayment;
            });
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: MColor.skin.withValues(alpha: 0.3),
            width: 1,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '转入账户',
              style: TextStyle(
                color: MColor.xFF777777,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: MColor.skin.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.add,
                    color: MColor.skin,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '选择转入账户',
                    style: TextStyle(
                      color: MColor.skin,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: MColor.xFF999999,
                  size: 16,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeeSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '手续费',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _feeController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: MColor.xFF1B1C1A,
            ),
            decoration: InputDecoration(
              hintText: '0.00',
              hintStyle: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF999999,
              ),
              prefixText: '¥ ',
              prefixStyle: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '转账金额',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: MColor.xFF1B1C1A,
            ),
            decoration: InputDecoration(
              hintText: '0.00',
              hintStyle: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF999999,
              ),
              prefixText: '¥ ',
              prefixStyle: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '备注',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _memoController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '添加备注信息（可选）',
              hintStyle: TextStyle(
                color: MColor.xFF999999,
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFF0F0F0), width: 1),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _toAccount != null ? _handleTransfer : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _toAccount != null ? MColor.skin : MColor.xFF999999,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              '确认转账',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleTransfer() {
    final amount = _amountController.text.trim();
    if (amount.isEmpty || double.tryParse(amount) == null || double.parse(amount) <= 0) {
      showToast('请输入有效的转账金额');
      return;
    }
    if (_toAccount?.id == null) {
      showToast('请选择转入账户');
      return;
    }

    try {
      AccountRepo.tranfer(
          accountId: widget.fromAccountId,
          repaymentAccountId: '${_toAccount?.id}',
          repaymentPrice: amount,
          handlingFees: _feeController.text.trim().isEmpty ? '0.00' : _feeController.text.trim(),
          memo: _memoController.text);
      showToast('转账成功');
      RouterHelper.router.pop();
    } catch (e) {
      showToast('转账失败 $e');
    } finally {
      Loading.dismiss();
    }
  }
}
