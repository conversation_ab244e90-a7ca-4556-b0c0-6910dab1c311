import 'dart:async';
import 'dart:developer' as developer;
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/common/utils.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/detail_tab/detail_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/modules/plan_tab/plan_model.dart';
import 'package:qiazhun/modules/transaction_item_view.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/notice_carousel_view.dart';
import 'package:qiazhun/widgets/price_view.dart';
import 'package:qiazhun/widgets/progress_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

// final detailProvider = FutureProvider<String>((ref) async {
//   await Future.delayed(Duration(seconds: 2));
//   return '这是明细数据';
// });

class DetailTabPage extends StatefulWidget {
  const DetailTabPage({super.key});

  @override
  State<StatefulWidget> createState() => _DetailTabState();
}

class _DetailTabState extends State<DetailTabPage> {
  HomeDetailResp? _data;

  bool _hasShowAd = false;

  final GlobalKey<RefreshIndicatorState> _refreshKey = GlobalKey<RefreshIndicatorState>();
  String? _budgetType = '1';
  final Map<String, String> _budgetName = {'1': '本周预算', '2': '本月预算', '3': '本年预算'};
  final Map<String, String> _budgetSectionHeader = {'1': '周', '2': '月', '3': '年'};

  final ScrollController _scrollController = ScrollController();
  final GlobalKey<NoticeCarouselViewState> _noticeCarouselKey = GlobalKey<NoticeCarouselViewState>();

  String? _tmpBookkeepingNumber;
  String? _currentRoute;
  Timer? _routeCheckTimer;
  StreamSubscription? _routeSubscription;

  @override
  void initState() {
    super.initState();
    _tmpBookkeepingNumber = UserStore.to.lastIndexBookkeepingNumber;

    // 添加滚动监听来检测 NoticeCarouselView 的可见性
    _scrollController.addListener(_onScroll);

    // 添加 GoRouter 路由监听
    RouterHelper.router.routerDelegate.addListener(_onRouteChanged);
    RouterHelper.router.routeInformationProvider.addListener(_onRouteChanged);

    // 添加定期路由检查机制，确保能捕获所有路由变化
    _startRouteCheckTimer();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _refreshKey.currentState?.show();

      // 初始化当前路由
      _currentRoute = RouterHelper.router.routerDelegate.currentConfiguration.fullPath;
      logger.i('DetailTabPage 初始路由: $_currentRoute');

      // 如果当前在详情页，启动轮播
      if (_currentRoute == Routes.detailTabPath) {
        _noticeCarouselKey.currentState?.startCarousel();
      }

      // if (!_hasShowAd) {
      //   _hasShowAd = true;
      //   RouterHelper.router.pushNamed(Routes.adPath);
      // }
    });
  }

  @override
  dispose() {
    RouterHelper.router.routerDelegate.removeListener(_onRouteChanged);
    RouterHelper.router.routeInformationProvider.removeListener(_onRouteChanged);
    _routeCheckTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  // GoRouter 路由变化监听
  void _onRouteChanged() {
    _checkRouteChange();
  }

  // 启动定期路由检查定时器
  void _startRouteCheckTimer() {
    _routeCheckTimer?.cancel();
    _routeCheckTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
      _checkRouteChange();
    });
  }

  // 检查路由变化的核心逻辑
  void _checkRouteChange() {
    if (!mounted) return;

    try {
      final newRoute = RouterHelper.router.routerDelegate.currentConfiguration.fullPath;
      final routeInfo = RouterHelper.router.routeInformationProvider.value;

      if (_currentRoute != newRoute) {
        logger.i('DetailTabPage 路由变化: $_currentRoute -> $newRoute');
        logger.i('DetailTabPage 路由信息: ${routeInfo.uri}');

        final oldRoute = _currentRoute;
        _currentRoute = newRoute;

        // 判断是否离开或进入详情页
        if (oldRoute == Routes.detailTabPath && newRoute != Routes.detailTabPath) {
          // 离开详情页
          logger.i('DetailTabPage 离开详情页，暂停轮播');
          _noticeCarouselKey.currentState?.pauseCarousel();
        } else if (oldRoute != Routes.detailTabPath && newRoute == Routes.detailTabPath) {
          // 进入详情页
          logger.i('DetailTabPage 进入详情页，启动轮播');
          _noticeCarouselKey.currentState?.startCarousel();
        } else if (oldRoute == Routes.detailTabPath && newRoute == Routes.detailTabPath) {
          // 在详情页内的路由变化（比如 pushNamed 到其他页面再返回）
          logger.i('DetailTabPage 详情页内路由变化，检查是否需要重新启动轮播');
          // 检查是否真的在详情页面
          if (routeInfo.uri.path == Routes.detailTabPath) {
            _noticeCarouselKey.currentState?.startCarousel();
          } else {
            _noticeCarouselKey.currentState?.pauseCarousel();
          }
        }
      }
    } catch (e) {
      logger.e('检查路由变化时出错: $e');
    }
  }

  // 滚动监听方法
  void _onScroll() {
    if (!mounted || _noticeCarouselKey.currentState == null) return;

    // 检测 NoticeCarouselView 是否在可见区域内
    _checkNoticeVisibility();
  }

  // 检测通知组件的可见性
  void _checkNoticeVisibility() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _noticeCarouselKey.currentState == null) return;

      try {
        final RenderBox? renderBox = _noticeCarouselKey.currentContext?.findRenderObject() as RenderBox?;
        if (renderBox == null) return;

        final Offset position = renderBox.localToGlobal(Offset.zero);
        final Size size = renderBox.size;
        final Size screenSize = MediaQuery.of(context).size;

        // 检查组件是否在屏幕可见区域内
        final bool isVisible =
            position.dy < screenSize.height && position.dy + size.height > 0 && position.dx < screenSize.width && position.dx + size.width > 0;

        // 设置可见性
        _noticeCarouselKey.currentState?.setVisibility(isVisible);
      } catch (e) {
        logger.e('检测通知可见性时出错: $e');
      }
    });
  }

  Future<void> _getData() async {
    // Loading.show();
    try {
      var resp = await DetailRepo.getHomeDetail(budgetType: _budgetType ?? '1', bookkeepingNumber: _tmpBookkeepingNumber);
      if (resp.code == 1) {
        UserStore.to.lastIndexBookkeepingNumber = resp.data?.bookkeepingNumber;
        _tmpBookkeepingNumber = UserStore.to.lastIndexBookkeepingNumber;
        setState(() {
          _data = resp.data;
          _budgetType = _data?.budgetArr?.firstWhereOrNull((el) => el.budgetType.isNotEmpty == true)?.budgetType;
        });
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace, maxFrames: 10);
      logger.e(e);
      showToast(e.toString());
    } finally {
      // Loading.dismiss();
    }
  }

  List<dynamic> noticeData = [
    {'title': '今天有卡片需要还款', 'type': 1},
    {'title': '今天有卡片已经逾期', 'type': 2},
    {'title': '今天有卡片需要扣年费', 'type': 3},
    {'title': '今天有借出账款需要讨要', 'type': 4}
  ];

  @override
  Widget build(BuildContext context) {
    // final detail = ref.watch(detailProvider);
    List<Widget> headSections = [];
    if (_data != null) {
      if (_data!.assets != null) {
        headSections.add(_totalAssetView);
      }
      if (noticeData.isNotEmpty == true) {
        headSections.add(_noticeView);
      }
      if (_data!.budgetArr?.isNotEmpty == true) {
        headSections.add(_budgetView);
      }
      if (_data!.planArr?.isNotEmpty == true) {
        headSections.add(_savingPlanView);
      }
      if (_data!.account != null) {
        headSections.add(_detailHeaderView);
      }
    }
    return Scaffold(
      // appBar: AppBar(
      //   backgroundColor: Colors.transparent,
      // ),
      extendBody: true,

      // extendBodyBehindAppBar: true,
      // resizeToAvoidBottomInset: false,
      // appBar: PreferredSize(
      //   preferredSize: Size.fromHeight(203.0),
      //   child: Container(
      //     height: 203,
      //     width: double.infinity,
      //     child: Image.asset('assets/images/icon_title.png', width: 129, height: 30, fit: BoxFit.contain),
      //     decoration: const BoxDecoration(
      //       gradient: LinearGradient(
      //           colors: [Color(0xFFD7F5E6), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
      //     ),
      //   ),),
      floatingActionButton: GestureDetector(
        onTap: () {
          RouterHelper.router.pushNamed(Routes.bookkeepingPath).then((_) {
            _getData();
          });
        },
        child: Image.asset(
          'assets/images/ic_add.png',
          height: 50,
          width: 50,
        ),
      ),
      body: SafeArea(
        // maintainBottomViewPadding: true,
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      // flexibleSpace: Container(
                      //   decoration: const BoxDecoration(
                      //     gradient: LinearGradient(
                      //         colors: [Color(0xFFD7F5E6), Color(0xFFAEE7CD)],
                      //         begin: Alignment.bottomCenter,
                      //         end: Alignment.topCenter,
                      //         tileMode: TileMode.clamp),
                      //   ),
                      // ),
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      actions: [
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            RouterHelper.router.pushNamed(Routes.searchPath);
                          },
                          child: Container(
                            margin: EdgeInsets.only(right: 12),
                            padding: EdgeInsets.only(right: 6),
                            decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(18)),
                            height: 35,
                            child: Row(
                              children: [
                                Container(
                                    margin: EdgeInsets.only(left: 4, right: 4),
                                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                                    decoration: BoxDecoration(color: MColor.xFFF4FEFA, borderRadius: BorderRadius.circular(15)),
                                    child: Text(
                                      '输入你要搜索的关键词',
                                      style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF577F8C),
                                    )),
                                Image.asset(
                                  'assets/images/ic_search.png',
                                  width: 18,
                                  height: 18,
                                  fit: BoxFit.fill,
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                      title: Image.asset(
                        'assets/images/icon_title.png',
                        width: 129,
                        height: 30,
                      ))),
              Positioned.fill(
                top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                // top: 0,
                child: RefreshIndicator(
                  key: _refreshKey,
                  triggerMode: RefreshIndicatorTriggerMode.anywhere,
                  onRefresh: () async {
                    await _getData();
                  },
                  child: ListView.separated(
                    controller: _scrollController,
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, index) {
                      if (index < headSections.length) {
                        return headSections[index];
                      }
                      return _dailyTransactionView(_data!.flowingWaterLog![index - headSections.length]);
                    },
                    separatorBuilder: (context, index) {
                      if (index >= headSections.length) {
                        return Column(
                          children: [
                            Divider(
                              height: 0.5,
                              thickness: 0.5,
                              color: MColor.xFFD9D9D9,
                              indent: 15,
                            ),
                            Container(
                              color: MColor.xFFF5F5F5,
                              height: 16,
                            )
                          ],
                        );
                      } else {
                        return Container(height: 8, color: MColor.xFFF5F5F5);
                      }
                      // return SizedBox();
                    },
                    itemCount: headSections.length + (_data?.flowingWaterLog?.length ?? 0),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _totalAssetView {
    return Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(15), bottom: Radius.circular(15))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    style: const ButtonStyle(
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                    ),
                    onPressed: () {
                      List<String> selected = UserStore.to.lastIndexBookkeepingNumber == null ? [] : [UserStore.to.lastIndexBookkeepingNumber!];
                      RouterHelper.router.pushNamed(Routes.chooseLedgarPath, extra: {'selected': selected, 'multiSelect': false}).then((value) {
                        if (value is Map && value.containsKey('selected') && value['selected'] is List && value['selected'].isNotEmpty == true) {
                          _tmpBookkeepingNumber = value['selected'][0];
                          _refreshKey.currentState?.show();
                        }
                      });
                      // RouterHelper.router.pushNamed(Routes.ledgarListPath);
                    },
                    icon: Image.asset(
                      'assets/images/ic_ledger.png',
                      width: 32,
                      height: 32,
                    )),
                const SizedBox(
                  width: 8,
                ),
                IconButton(
                    padding: EdgeInsets.zero,
                    style: const ButtonStyle(
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                    ),
                    constraints: const BoxConstraints(),
                    onPressed: () {
                      RouterHelper.router.pushNamed(Routes.calendarPath);
                    },
                    icon: Image.asset('assets/images/ic_calendar.png', width: 32, height: 32)),
                const Spacer(),
                IconButton(
                    padding: EdgeInsets.zero,
                    style: const ButtonStyle(
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                    ),
                    constraints: const BoxConstraints(),
                    onPressed: () {
                      RouterHelper.router.pushNamed(Routes.budgetSettingPath).then((_) {
                        _refreshKey.currentState?.show();
                      });
                    },
                    icon: Image.asset('assets/images/ic_setting.png', width: 32, height: 32))
              ],
            ),
            SizedBox(
              height: 164,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  RouterHelper.router.pushNamed(Routes.myAssetsPath);
                },
                child: Stack(
                  children: [
                    Positioned.fill(
                        top: 15,
                        bottom: 0,
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(15, 15, 15, 0),
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(15), color: MColor.skin),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                children: [
                                  Image.asset(
                                    'assets/images/ic_rings.png',
                                    width: 28,
                                    height: 17,
                                  ),
                                  const SizedBox(
                                    width: 6,
                                  ),
                                  Text('${_data?.indexModule ?? ''}', style: TextStyle(height: 1, color: Color.fromARGB(0xFF, 168, 220, 217), fontSize: 14)),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  IconButton(
                                      padding: EdgeInsets.zero,
                                      style: const ButtonStyle(
                                        tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                                      ),
                                      constraints: const BoxConstraints(),
                                      onPressed: () {},
                                      icon: const Icon(Icons.visibility_outlined, size: 16, color: Colors.white))
                                ],
                              ),
                              const SizedBox(
                                height: 8,
                              ),
                              Row(
                                children: [
                                  Expanded(
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      alignment: Alignment.centerLeft,
                                      child: PriceView(
                                        price: PriceInfo.parsePrice(_data?.assets?.currentAssetsTotal ?? '0.00'),
                                        integerFontSize: 34,
                                        fractionalFontSize: 20,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 144,
                                    height: 50,
                                  )
                                ],
                              ),
                              const SizedBox(
                                height: 22,
                              ),
                              Row(
                                children: [
                                  Expanded(
                                      child: RichText(
                                    text: WidgetSpan(
                                        child: GestureDetector(
                                      onTap: () {
                                        RouterHelper.router.pushNamed(Routes.moneyFlowPath, extra: {'page_type': 1, 'time_range_type': '2'});
                                      },
                                      child: Row(
                                        children: [
                                          PriceView(
                                              price: PriceInfo.parsePrice(_data?.assets?.incomeTotal ?? '0.00'),
                                              integerFontSize: 20,
                                              fractionalFontSize: 14,
                                              prefix: '收入',
                                              prefixStyle: TextStyle(height: 1, color: Color.fromARGB(255, 168, 220, 217), fontSize: 12),
                                              prefixPadding: 10)
                                        ],
                                      ),
                                    )),
                                  )),
                                  const SizedBox(
                                    width: 12,
                                  ),
                                  Expanded(
                                      child: RichText(
                                    textAlign: TextAlign.end,
                                    text: WidgetSpan(
                                        child: GestureDetector(
                                      onTap: () => RouterHelper.router.pushNamed(Routes.moneyFlowPath, extra: {'page_type': 2, 'time_range_type': '2'}),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          PriceView(
                                              price: PriceInfo.parsePrice(_data?.assets?.expenditureTotal ?? '0.00'),
                                              integerFontSize: 20,
                                              fractionalFontSize: 14,
                                              prefix: '支出',
                                              prefixStyle: TextStyle(height: 1, color: Color.fromARGB(255, 168, 220, 217), fontSize: 12),
                                              prefixPadding: 10),
                                        ],
                                      ),
                                    )),
                                  ))
                                ],
                              )
                            ],
                          ),
                        )),
                    Positioned(
                        top: 0,
                        right: 20,
                        child: Image.asset(
                          'assets/images/cat.png',
                          width: 124,
                          height: 124,
                        ))
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget get _noticeView {
    return NoticeCarouselView(
      key: _noticeCarouselKey,
      noticeData: noticeData,
      onNoticeTap: (notice) {
        // RouterHelper.router.pushNamed(Routes.noticePath, extra: {'notice_type': notice['type']});
      },
    );
  }

  Widget get _budgetView {
    List<Widget> headerSections = [];
    _data!.budgetArr?.forEach((el) {
      headerSections.add(GestureDetector(
        onTap: () {
          if (_budgetType != el.budgetType) {
            setState(() {
              _budgetType = el.budgetType;
            });
          }
        },
        child: Container(
          width: 48,
          height: double.infinity,
          decoration: BoxDecoration(color: _budgetType != el.budgetType ? Colors.transparent : MColor.skin, borderRadius: BorderRadius.circular(12)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _budgetSectionHeader[el.budgetType] ?? '',
                style: TextStyle(height: 1.4, fontSize: 12, color: _budgetType != el.budgetType ? MColor.skin : Colors.white),
              ),
            ],
          ),
        ),
      ));
    });
    return Container(
      padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
      color: const Color(0xFFF5F5F5),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                _budgetName[_budgetType]!,
                style: TextStyle(fontSize: 17, height: 1, fontWeight: FontWeight.w500, color: Colors.black),
              ),
              const Spacer(),
              Container(
                height: 26,
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(border: Border.all(color: MColor.skin, width: 0.5), color: MColor.xFFF4FEFA, borderRadius: BorderRadius.circular(14)),
                child: Row(
                  children: headerSections,
                ),
              )
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          Builder(builder: (context) {
            BudgetSetting? budgetSetting;
            if (_data?.budgetArr?.isNotEmpty == true) {
              for (BudgetSetting setting in _data!.budgetArr!) {
                if (setting.budgetType == _budgetType) {
                  budgetSetting = setting;
                }
              }
            }
            if (budgetSetting == null) {
              return GestureDetector(
                onTap: () {
                  RouterHelper.router.pushNamed(Routes.budgetSettingPath, extra: {'expand': _budgetType}).then((value) {
                    _getData();
                  });
                },
                child: Container(
                    height: 134,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: Colors.white),
                    padding: EdgeInsets.fromLTRB(25, 14, 25, 14),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.add, color: MColor.skin),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '设置预算',
                              style: TextStyle(fontSize: 14, height: 1.4, color: MColor.skin),
                            ),
                          ],
                        )
                      ],
                    )),
              );
            }
            return GestureDetector(
              onTap: () => RouterHelper.router.pushNamed(Routes.moneyFlowPath, extra: {'page_type': 3, 'time_range_type': _budgetType ?? '1'}),
              child: Container(
                height: 134,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: Colors.white),
                padding: EdgeInsets.fromLTRB(25, 14, 25, 14),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        PriceView(
                          price: PriceInfo.parsePrice(budgetSetting.budgetAmount ?? '0.00'),
                          integerFontSize: 17,
                          fractionalFontSize: 12,
                          textColor: MColor.xFFCB322E,
                          fontWeight: FontWeight.w500,
                        ),
                        PriceView(
                            price: PriceInfo.parsePrice(budgetSetting.expenditure ?? '0.00'),
                            integerFontSize: 14,
                            fractionalFontSize: 10,
                            textColor: MColor.xFFCB322E,
                            fontWeight: FontWeight.w500,
                            prefix: '剩余',
                            prefixStyle: TextStyle(height: 1, color: MColor.xFF999999, fontSize: 12),
                            prefixPadding: 4),
                      ],
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          budgetSetting.startDate,
                          style: TextStyle(height: 1.3, color: MColor.xFF999999, fontSize: 12),
                        ),
                        Text(
                          budgetSetting.endDate ?? '',
                          style: TextStyle(height: 1.3, color: MColor.xFF999999, fontSize: 12),
                        )
                      ],
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    Builder(builder: (context) {
                      var fullWidth = MediaQuery.of(context).size.width - 15 - 25 - 15 - 25;
                      return SizedBox(
                          height: 19,
                          child: Stack(
                            children: [
                              Container(
                                width: fullWidth,
                                decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      // begin: Alignment.topRight,
                                      // end: Alignment.bottomLeft,
                                      stops: [
                                        0.0,
                                        ((double.tryParse(budgetSetting?.proportion ?? '') ?? 0) / 100),
                                        ((double.tryParse(budgetSetting?.proportion ?? '') ?? 0) / 100),
                                        1.0
                                      ],
                                      begin: const FractionalOffset(0.0, 0.0),
                                      end: const FractionalOffset(1.0, 0.0),
                                      colors: [
                                        MColor.skin,
                                        MColor.skin,
                                        MColor.skin.withOpacity(0.2),
                                        MColor.skin.withOpacity(0.2),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                            ],
                          ));
                    }),
                    const SizedBox(
                      height: 8,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        PriceView(
                            price: PriceInfo.parsePrice(budgetSetting.spendPrice ?? '0.00'),
                            integerFontSize: 14,
                            fractionalFontSize: 10,
                            textColor: MColor.xFF999999,
                            prefix: '支出',
                            prefixStyle: TextStyle(height: 1, color: MColor.xFF999999, fontSize: 12),
                            prefixPadding: 4),
                        budgetSetting.surplusStatus == 0
                            ? RichText(
                                text: TextSpan(
                                text: '超支',
                                // children: [
                                //   TextSpan(text: ' 已超支', style: TextStyle(fontSize: 12, height: 1, color: MColor.xFF999999, fontWeight: FontWeight.w500))
                                // ],
                                style: TextStyle(height: 1, color: MColor.xFFCB322E, fontSize: 12),
                              ))
                            : PriceView(
                                price: PriceInfo.parsePrice(budgetSetting.dayPrice ?? '0.00'),
                                integerFontSize: 14,
                                fractionalFontSize: 10,
                                textColor: MColor.xFF999999,
                                prefix: '剩余日均',
                                prefixStyle: TextStyle(height: 1, color: MColor.xFF999999, fontSize: 12),
                                prefixPadding: 4),
                      ],
                    ),
                  ],
                ),
              ),
            );
          })
        ],
      ),
    );
  }

  Widget get _detailHeaderView {
    return Builder(builder: (context) {
      return Container(
        color: const Color(0xFFF5F5F5),
        padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '账目明细',
                  style: TextStyle(fontSize: 17, height: 1, fontWeight: FontWeight.w500, color: Colors.black),
                ),
                GestureDetector(
                  onTap: () {
                    RouterHelper.router.pushNamed(Routes.bookkeepingPath).then((_) {
                      _getData();
                    });
                  },
                  child: Text(
                    '+记一笔',
                    style: TextStyle(height: 1.5, color: MColor.xFFCB322E, fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 8,
            ),
            GestureDetector(
              onTap: () {
                RouterHelper.router.pushNamed(Routes.moneyFlowPath, extra: {'page_type': 3, 'time_range_type': '2'});
              },
              child: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: Colors.white),
                padding: EdgeInsets.fromLTRB(25, 12, 25, 24),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('本月结余', style: TextStyle(height: 1, fontSize: 17, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A)),
                        Builder(builder: (context) {
                          PriceInfo balance = PriceInfo.parsePrice(_data?.account?.monthBalance ?? '0.00');
                          return PriceView(
                              price: balance,
                              integerFontSize: 17,
                              fractionalFontSize: 14,
                              fontWeight: FontWeight.w500,
                              textColor: balance.isNegative ? MColor.xFF68C2BF : MColor.xFFCB322E);
                        })
                      ],
                    ),
                    const SizedBox(
                      height: 18,
                    ),
                    Row(
                      children: [
                        Text('本月支出', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999)),
                        const SizedBox(
                          width: 6,
                        ),
                        Builder(builder: (context) {
                          var fullWidth = (MediaQuery.of(context).size.width - 15 - 25 - 15 - 25) / 2;
                          var ratio = (double.tryParse(_data?.account?.monthExpenditureProportion ?? '') ?? 0.0) / 100;
                          return ProgressView(width: fullWidth, height: 12, progress: ratio, color: MColor.skin);
                        }),
                        const SizedBox(
                          width: 10,
                        ),
                        const Spacer(),
                        PriceView(
                            price: PriceInfo.parsePrice(_data?.account?.monthExpenditure ?? '0.0'),
                            integerFontSize: 12,
                            fractionalFontSize: 10,
                            textColor: MColor.xFF999999),
                      ],
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Row(
                      children: [
                        Text('本月收入', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999)),
                        const SizedBox(
                          width: 6,
                        ),
                        Builder(builder: (context) {
                          var fullWidth = (MediaQuery.of(context).size.width - 15 - 25 - 15 - 25) / 2;
                          var ratio = (double.tryParse(_data?.account?.monthIncomeProportion ?? '') ?? 0.0) / 100;
                          return ProgressView(width: fullWidth, height: 12, progress: ratio, color: MColor.xFFCB322E);
                        }),
                        const SizedBox(
                          width: 10,
                        ),
                        const Spacer(),
                        PriceView(
                            price: PriceInfo.parsePrice(_data?.account?.monthIncome ?? '0.0'),
                            integerFontSize: 12,
                            fractionalFontSize: 10,
                            textColor: MColor.xFF999999),
                      ],
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      );
    });
  }

  Widget get _savingPlanView {
    return Builder(builder: (context) {
      return Container(
        color: const Color(0xFFF5F5F5),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const SizedBox(
                  width: 15,
                ),
                Text(
                  '存钱计划',
                  style: TextStyle(fontSize: 17, height: 1, fontWeight: FontWeight.w500, color: Colors.black),
                ),
              ],
            ),
            const SizedBox(
              height: 8,
            ),
            // SizedBox(
            //   width: MediaQuery.of(context).size.width - 30,
            //   height: 141,
            //   child: ListView.separated(
            //       physics: ScrollPhysics(),
            //       shrinkWrap: true,
            //       scrollDirection: Axis.horizontal,
            //       itemBuilder: (context, index) {
            //         return _savingPlanView;
            //       },
            //       separatorBuilder: (context, index) {
            //         return SizedBox(
            //           width: 8,
            //         );
            //       },
            //       itemCount: 2),
            // ),
            Builder(builder: (context) {
              List<Widget> planWidgets = [];
              if (_data?.planArr?.isNotEmpty == true) {
                for (PlanInfo plan in _data!.planArr!) {
                  planWidgets.add((_savingPlanCard(plan)));
                  planWidgets.add(
                    const SizedBox(
                      width: 8,
                    ),
                  );
                }
              }
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    const SizedBox(
                      width: 15,
                    ),
                    ...planWidgets,
                    const SizedBox(
                      width: 15,
                    ),
                  ],
                ),
              );
            })
          ],
        ),
      );
    });
  }

  Widget _savingPlanCard(PlanInfo planInfo) {
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.planDetailPath, pathParameters: {'planId': '${planInfo.id}'}).then((value) {
          _getData();
        });
      },
      child: Container(
        width: 297,
        height: 141,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(28), color: Colors.white),
        child: Stack(
          children: [
            Positioned.fill(
              left: 25,
              top: 11,
              right: 25,
              child: Column(
                children: [
                  Row(
                    children: [
                      if (planInfo.icon?.isNotEmpty == true) RoundImage(imageUrl: planInfo.icon!, size: 48, radius: 24),
                      const SizedBox(
                        width: 11,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(planInfo.name,
                                maxLines: 1,
                                textAlign: TextAlign.start,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(height: 1, fontSize: 17, color: MColor.xFF1B1C1A)),
                            const SizedBox(
                              height: 4,
                            ),
                            Text('¥${planInfo.requiredAmount}', style: TextStyle(height: 1, fontSize: 17, color: MColor.xFFCB322E))
                          ],
                        ),
                      ),
                      const SizedBox(
                        width: 40,
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '${planInfo.proportion}%',
                        style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999),
                      ),
                    ],
                  ),
                  Stack(
                    children: [
                      ProgressView(width: 249, height: 19, progress: ((double.tryParse(planInfo.proportion ?? '') ?? 0) / 100), color: MColor.skin),
                    ],
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Text(
                            '已存入：',
                            style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999),
                          ),
                          PriceView(
                            price: PriceInfo.parsePrice(planInfo.accumulatedAmount ?? '0.00'),
                            integerFontSize: 12,
                            fractionalFontSize: 10,
                            textColor: MColor.xFF999999,
                            fontWeight: FontWeight.w500,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            '剩余: ',
                            style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF999999),
                          ),
                          PriceView(
                            price: PriceInfo.parsePrice(planInfo.residuePrice ?? '0.00'),
                            integerFontSize: 12,
                            fractionalFontSize: 10,
                            textColor: MColor.xFFCB322E,
                            fontWeight: FontWeight.w500,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: () {
                    RouterHelper.router.pushNamed(Routes.addSavingDialogPath, pathParameters: {'planId': '${planInfo.id}'}).then((_) {
                      _getData();
                    });
                  },
                  child: Container(
                    height: 36,
                    padding: EdgeInsets.fromLTRB(12, 8, 12, 8),
                    decoration:
                        BoxDecoration(color: MColor.xFFFFD180, borderRadius: BorderRadius.only(topRight: Radius.circular(28), bottomLeft: Radius.circular(28))),
                    child: Text(
                      '攒一笔',
                      style: TextStyle(height: 1.4, color: Colors.black, fontSize: 14),
                    ),
                  ),
                ))
          ],
        ),
      ),
    );
  }

  Widget _dailyTransactionView(FlowingWaterLog data) {
    return ListView.separated(
        padding: EdgeInsets.zero,
        controller: _scrollController,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Container(
              color: MColor.xFFF5F5F5,
              child: TransactionItemView(
                data.item![index],
                headerView: index == 0 ? _dailyTransactionHeaderView(data) : null,
                actions: [
                  TransactionItemAction(
                    label: '删除',
                    icon: Icons.delete,
                    bgColor: MColor.xFFFF7858,
                    fgColor: MColor.xFFFFFFFF,
                    onPressed: () {
                      showCustomDialog(
                        '确认删除',
                        content: '删除后不可恢复，请确认是否删除',
                        cancel: true,
                        onConfirm: () async {
                          Loading.show();
                          try {
                            var resp = await BookkeepingRepo.deleteBill('${data.item![index].id}');
                            if (resp.code == 1) {
                              showToast('删除成功');
                              _getData();
                            } else {
                              showToast(resp.msg ?? '删除失败');
                            }
                          } catch (e) {
                            showToast('删除失败 $e');
                          } finally {
                            Loading.dismiss();
                          }
                        },
                        onCancel: () {},
                      );
                    },
                  ),
                  TransactionItemAction(
                    bgColor: MColor.xFFFFBE4A,
                    fgColor: MColor.xFFFFFFFF,
                    icon: Icons.edit,
                    label: '编辑',
                    onPressed: () async {
                      RouterHelper.router.pushNamed(Routes.bookkeepingPath, extra: {'logId': '${data.item![index].id}'}).then((_) {
                        _getData();
                      });
                    },
                  )
                ],
              ));
        },
        separatorBuilder: (context, index) {
          return Divider(
            height: 0.5,
            thickness: 0.5,
            color: MColor.xFFD9D9D9,
            indent: 15,
          );
        },
        itemCount: data.item?.length ?? 0);
  }

  Widget _dailyTransactionHeaderView(FlowingWaterLog data) {
    return Row(
      children: [
        const SizedBox(
          width: 14,
        ),
        Container(
          height: 13,
          width: 3,
          decoration: BoxDecoration(color: MColor.xFFFFD180, borderRadius: BorderRadius.circular(2)),
        ),
        const SizedBox(
          width: 4,
        ),
        Text(
          data.date ?? '',
          style: TextStyle(fontSize: 15, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF1B1C1A),
        ),
        const SizedBox(
          width: 12,
        ),
        Text(
          data.weekChinese ?? '',
          style: TextStyle(fontSize: 15, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF1B1C1A),
        ),
        const Spacer(),
        PriceView(
          price: PriceInfo.parsePrice(data.total ?? '0.0'),
          integerFontSize: 14,
          fractionalFontSize: 10,
          textColor: MColor.xFF999999,
          prefix: '总计',
          prefixStyle: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF999999),
        ),
        const SizedBox(
          width: 14,
        ),
      ],
    );
  }
}
