import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/models/home_detail_model.dart';

class DetailRepo {
  static String postDetailHomePage = 'api/detail/homePage';

  static Future<BaseModel<List<NoticeData>>> getNoticeData() async {
    var resp = await HttpUtil().post(
      'api/user/notificationSummary',
    );
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => NoticeData.fromJson(e)).toList());
  }

  static Future<BaseModel<HomeDetailResp?>> getHomeDetail({required String budgetType, String? bookkeepingNumber}) async {
    var data = {"budgetType": budgetType};
    if (bookkeepingNumber?.isNotEmpty == true) {
      data['bookkeepingNumber'] = bookkeepingNumber!;
    }
    var response = await HttpUtil().post(postDetailHomePage, data: data);
    return BaseModel.fromJson(response, (json) => HomeDetailResp.fromJson(json));
  }

  static Future<BaseModel<List<TransactionItem>>> search({
    String? keyword,
    required String action,
  }) async {
    var data = {};
    if (action == '1' && keyword?.isNotEmpty == true) {
      data['keyWords'] = keyword!;
    }
    data['action'] = action;
    var resp = await HttpUtil().post('api/detail/searchFlowingWater', data: data);
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => TransactionItem.fromJson(e)).toList());
  }

  static Future<BaseModel<MoneyLogResp>> getMonthMoneyLogList({
    required String timeInterval,
    required String type,
    required String order,
  }) async {
    var data = {
      "timeInterval": timeInterval, //月份
      "type": type, //类型:1=收入 2=支出
      "order": order //排序类型:1=金额 2时间
    };
    var resp = await HttpUtil().post('api/money_log/getMonthMoneyLogList', data: data);
    return BaseModel.fromJson(resp, (json) => MoneyLogResp.fromJson(json));
  }

  static Future<BaseModel<List<CategoryFlowLog>>> getCategoryMoneyLog({
    required String timeInterval,
    required String order,
    required String categoryId,
  }) async {
    var data = {
      "timeInterval": timeInterval, //月份,
      "categoryId": categoryId,
      "order": order //排序类型:1=金额 2时间
    };
    var resp = await HttpUtil().post('api/chart/getCategoryMoneyLog', data: data);
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => CategoryFlowLog.fromJson(e)).toList());
  }
}
