import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:qiazhun/modules/bank_list/bank_list_bloc.dart';
import 'package:qiazhun/modules/bank_list/bank_list_event.dart';
import 'package:qiazhun/modules/bank_list/bank_list_state.dart';

void main() {
  group('BankListBloc', () {
    late BankListBloc bankListBloc;

    setUp(() {
      bankListBloc = BankListBloc();
    });

    tearDown(() {
      bankListBloc.close();
    });

    test('initial state is BankListInitial', () {
      expect(bankListBloc.state, equals(const BankListInitial()));
    });

    group('LoadBankListEvent', () {
      blocTest<BankListBloc, BankListState>(
        'emits [BankListLoading, BankListError] when LoadBankListEvent is added and fails due to missing dependencies',
        build: () => bankListBloc,
        act: (bloc) => bloc.add(const LoadBankListEvent(onlyBank: false)),
        expect: () => [
          const BankListLoading(),
          // In test environment, this will fail due to missing GetX UserStore dependency
          isA<BankListError>(),
        ],
        wait: const Duration(seconds: 5),
      );
    });

    group('RefreshBankListEvent', () {
      blocTest<BankListBloc, BankListState>(
        'emits [BankListLoading, BankListError] when RefreshBankListEvent is added and fails due to missing dependencies',
        build: () => bankListBloc,
        act: (bloc) => bloc.add(const RefreshBankListEvent(onlyBank: true)),
        expect: () => [
          const BankListLoading(),
          // In test environment, this will fail due to missing GetX UserStore dependency
          isA<BankListError>(),
        ],
        wait: const Duration(seconds: 5),
      );
    });
  });
}
